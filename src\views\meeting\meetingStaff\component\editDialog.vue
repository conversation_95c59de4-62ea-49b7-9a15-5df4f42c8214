<script lang="ts" name="meetingStaff" setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { Plus } from '@element-plus/icons-vue';
import type { FormRules, UploadProps } from "element-plus";
import { formatDate } from '/@/utils/formatTime';
import { useMeetingStaffApi } from '/@/api/meeting/meetingStaff';
import { getAPI } from '/@/utils/axios-utils';
import { SysFileApi } from '/@/api-services/api';
import { SysFile } from '/@/api-services/models';

//父级传递来的函数，用于回调
const emit = defineEmits(["reloadTable"]);
const meetingStaffApi = useMeetingStaffApi();
const ruleFormRef = ref();

const state = reactive({
	title: '',
	loading: false,
	showDialog: false,
	ruleForm: {} as any,
	stores: {},
	dropdownData: {} as any,
	avatarUrl: '',
	uploadLoading: false,
});

// 自行添加其他规则
const rules = ref<FormRules>({
	email: [
		{ pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/, message: '请输入正确的邮箱格式', trigger: 'blur' }
	],
	phone: [
		{ pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' }
	]
});

// 页面加载时
onMounted(async () => {
});

// 打开弹窗
const openDialog = async (row: any, title: string) => {
	state.title = title;
	row = row ?? {  };
	state.ruleForm = row.id ? await meetingStaffApi.detail(row.id).then(res => res.data.result) : JSON.parse(JSON.stringify(row));
	state.showDialog = true;
};

// 关闭弹窗
const closeDialog = () => {
	emit("reloadTable");
	state.showDialog = false;
};

// 头像上传前的校验
const beforeAvatarUpload = (rawFile: any) => {
	const isJPG = rawFile.type === 'image/jpeg' || rawFile.type === 'image/png' || rawFile.type === 'image/gif';
	const isLt2M = rawFile.size / 1024 / 1024 < 2;

	if (!isJPG) {
		ElMessage.error('头像图片只能是 JPG/PNG/GIF 格式!');
		return false;
	}
	if (!isLt2M) {
		ElMessage.error('头像图片大小不能超过 2MB!');
		return false;
	}
	return true;
};

// 自定义头像上传
const uploadAvatar = async (options: any) => {
	state.uploadLoading = true;
	try {
		const res = await getAPI(SysFileApi).apiSysFileUploadAvatarPostForm(options.file);
		const fileUrl = getFileUrl(res.data.result!);
		state.ruleForm.avatar_url = fileUrl;
		ElMessage.success('头像上传成功!');
	} catch (error) {
		ElMessage.error('头像上传失败!');
	} finally {
		state.uploadLoading = false;
	}
};

// 头像上传成功回调
const handleAvatarSuccess = (response: any, file: any) => {
	console.log('头像上传成功', response, file);
};

// 获取文件地址
const getFileUrl = (row: SysFile): string => {
	if (row.bucketName == 'Local') {
		return `/${row.filePath}/${row.id}${row.suffix}`;
	} else {
		return row.url!;
	}
};

// 提交
const submit = async () => {
	ruleFormRef.value.validate(async (isValid: boolean, fields?: any) => {
		if (isValid) {
			let values = state.ruleForm;
			await meetingStaffApi[state.ruleForm.id ? 'update' : 'add'](values);
			closeDialog();
		} else {
			ElMessage({
				message: `表单有${Object.keys(fields).length}处验证失败，请修改后再提交`,
				type: "error",
			});
		}
	});
};

//将属性或者函数暴露给父组件
defineExpose({ openDialog });
</script>
<template>
	<div class="meetingStaff-container">
		<el-dialog v-model="state.showDialog" :width="800" draggable :close-on-click-modal="false">
			<template #header>
				<div style="color: #fff">
					<span>{{ state.title }}</span>
				</div>
			</template>
			<el-form :model="state.ruleForm" ref="ruleFormRef" label-width="auto" :rules="rules">
				<el-row :gutter="35">
					<el-form-item v-show="false">
						<el-input v-model="state.ruleForm.id" />
					</el-form-item>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="员工编号" prop="staff_code">
							<el-input v-model="state.ruleForm.staff_code" placeholder="请输入员工编号" maxlength="50" show-word-limit clearable :disabled="!!state.ruleForm.id" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="员工姓名" prop="staff_name">
							<el-input v-model="state.ruleForm.staff_name" placeholder="请输入员工姓名" maxlength="100" show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="职位" prop="position">
							<el-input v-model="state.ruleForm.position" placeholder="请输入职位" maxlength="100" show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="部门" prop="department">
							<el-input v-model="state.ruleForm.department" placeholder="请输入部门" maxlength="100" show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="邮箱" prop="email">
							<el-input v-model="state.ruleForm.email" placeholder="请输入邮箱" maxlength="100" show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="手机号" prop="phone">
							<el-input v-model="state.ruleForm.phone" placeholder="请输入手机号" maxlength="20" show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="头像" prop="avatar_url">
							<el-upload
								class="avatar-uploader"
								:show-file-list="false"
								:on-success="handleAvatarSuccess"
								:before-upload="beforeAvatarUpload"
								:http-request="uploadAvatar"
								accept=".jpg,.jpeg,.png,.gif"
								action="#"
							>
								<img v-if="state.ruleForm.avatar_url" :src="state.ruleForm.avatar_url" class="avatar" />
								<el-icon v-else class="avatar-uploader-icon" v-loading="state.uploadLoading"><Plus /></el-icon>
							</el-upload>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="自定义字段1" prop="field1">
							<el-input v-model="state.ruleForm.field1" placeholder="请输入自定义字段1" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="自定义字段2" prop="field2">
							<el-input v-model="state.ruleForm.field2" placeholder="请输入自定义字段2" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="自定义字段2" prop="field3">
							<el-input v-model="state.ruleForm.field3" placeholder="请输入自定义字段2" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="自定义字段2" prop="field4">
							<el-input v-model="state.ruleForm.field4" placeholder="请输入自定义字段2" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="自定义字段2" prop="field5">
							<el-input v-model="state.ruleForm.field5" placeholder="请输入自定义字段2" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="自定义字段2" prop="field6">
							<el-input v-model="state.ruleForm.field6" placeholder="请输入自定义字段2" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="描述" prop="description">
							<el-input v-model="state.ruleForm.description" placeholder="请输入描述" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col> 
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="() => state.showDialog = false">取 消</el-button>
					<el-button @click="submit" type="primary" v-reclick="1000">确 定</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>
<style lang="scss" scoped>
:deep(.el-select), :deep(.el-input-number) {
  width: 100%;
}

.avatar-uploader {
  :deep(.el-upload) {
    border: 1px dashed var(--el-border-color);
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);
  }

  :deep(.el-upload:hover) {
    border-color: var(--el-color-primary);
  }

  .el-icon.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    text-align: center;
  }
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
  object-fit: cover;
}
</style>