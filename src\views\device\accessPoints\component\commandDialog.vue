<script lang="ts" name="commandDialog" setup>
import { ref, reactive } from "vue";
import { ElMessage } from "element-plus";
import type { FormRules } from "element-plus";
import { useAccessPointsApi } from '/@/api/device/accessPoints';

//父级传递来的函数，用于回调
const emit = defineEmits(["reloadTable"]);
const accessPointsApi = useAccessPointsApi();
const ruleFormRef = ref();

const state = reactive({
	title: '',
	loading: false,
	showDialog: false,
	ruleForm: {
		mac_address: '',
		command: ''
	} as any,
});

// 表单验证规则
const rules = reactive<FormRules>({
	mac_address: [
		{ required: true, message: 'MAC地址不能为空', trigger: 'blur' },
	],
	command: [
		{ required: true, message: '命令不能为空', trigger: 'blur' },
	],
});

// 打开弹窗
const openDialog = (row: any = {}, title: string = '') => {
	state.ruleForm = {
		mac_address: row.mac_address || '',
		command: ''
	};
	state.title = title;
	state.showDialog = true;
};

// 关闭弹窗
const closeDialog = () => {
	state.showDialog = false;
	state.loading = false;
	ruleFormRef.value?.resetFields();
};

// 取消
const cancel = () => {
	closeDialog();
};

// 确定
const submit = () => {
	ruleFormRef.value?.validate(async (valid: boolean) => {
		if (!valid) return;
		try {
			state.loading = true;
			const data = {
				apMac: state.ruleForm.mac_address,
				command: state.ruleForm.command
			};
			await accessPointsApi.command(data);
			ElMessage.success('命令发送成功');
			closeDialog();
			emit('reloadTable');
		} catch (error) {
			console.error('命令发送失败:', error);
			ElMessage.error('命令发送失败');
		} finally {
			state.loading = false;
		}
	});
};

// 暴露方法给父组件
defineExpose({
	openDialog,
});
</script>

<template>
	<div class="commandDialog-container">
		<el-dialog v-model="state.showDialog" :title="state.title" width="500px" :close-on-click-modal="false"
			:close-on-press-escape="false" draggable>
			<el-form ref="ruleFormRef" :model="state.ruleForm" :rules="rules" label-width="80px" label-position="right">
				<el-row :gutter="35">
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item label="网关MAC" prop="mac_address">
							<el-input 
								v-model="state.ruleForm.mac_address" 
								placeholder="MAC地址" 
								readonly
								disabled
								style="text-transform: uppercase;"
							/>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item label="命令" prop="command">
							<el-input 
								v-model="state.ruleForm.command" 
								type="textarea"
								:rows="4"
								placeholder="请输入要发送的命令"
								clearable
							/>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="cancel">取消</el-button>
					<el-button type="primary" @click="submit" :loading="state.loading">确定</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<style scoped>
.commandDialog-container {
	padding: 0;
}

:deep(.el-input), :deep(.el-textarea) {
	width: 100%;
}

:deep(.el-form-item) {
	margin-bottom: 16px;
}

:deep(.el-input.is-disabled .el-input__inner) {
	background-color: #f5f7fa;
	color: #909399;
}
</style>