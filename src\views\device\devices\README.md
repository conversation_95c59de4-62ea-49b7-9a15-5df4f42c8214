# AP状态实时同步WebSocket功能说明

## 功能概述

本功能实现了AP（Access Point）状态的实时同步和WebSocket推送，包括以下核心组件：

1. **APSyncBackgroundService** - 后台服务，负责定时同步AP状态
2. **APStatusHub** - SignalR Hub，提供WebSocket连接和实时推送
3. **EstablishWebSocketConnectionAsync** - AccessPointsService中的方法，用于启动WebSocket功能

## 架构设计

### 1. 后台同步服务 (APSyncBackgroundService)

- **功能**：每30秒自动同步一次AP状态
- **流程**：
  1. 调用第三方API获取最新AP数据
  2. 与本地数据库中的AP数据进行比较
  3. 更新有变化的AP记录到数据库
  4. 通过SignalR推送状态变更通知

- **特性**：
  - 自动重试机制
  - 超时控制（25秒）
  - 批量更新优化
  - 详细的日志记录

### 2. SignalR Hub (APStatusHub)

- **端点**：`/hubs/apstatus`
- **功能**：
  - 客户端连接管理
  - 分组管理（AP状态组）
  - 实时状态推送
  - 当前状态查询

- **客户端方法**：
  - `APStatusUpdated` - 接收AP状态更新
  - `CurrentAPStatus` - 接收当前AP状态响应

- **服务端方法**：
  - `JoinAPStatusGroup` - 加入AP状态组
  - `LeaveAPStatusGroup` - 离开AP状态组
  - `RequestCurrentAPStatus` - 请求当前AP状态

## 使用方法

### 1. 服务端配置

#### 后台服务注册

服务已在 `Admin.NET.Application/Startup.cs` 中自动注册：

```csharp
[AppStartup(100)]
public class Startup : AppStartup
{
    public void ConfigureServices(IServiceCollection services)
    {
        // 注册AP状态同步后台服务
        services.AddHostedService<APSyncBackgroundService>();
    }
}
```

#### SignalR配置

SignalR服务已在 `Admin.NET.Web.Core/Startup.cs` 中配置：

```csharp
// 即时通讯
services.AddSignalR(options =>
{
    options.EnableDetailedErrors = true;
    options.KeepAliveInterval = TimeSpan.FromSeconds(15); // 服务器端向客户端ping的间隔
    options.ClientTimeoutInterval = TimeSpan.FromSeconds(30); // 客户端向服务器端ping的间隔
    options.MaximumReceiveMessageSize = 1024 * 1014 * 10; // 数据包大小10M
});
```

### 2. 客户端连接

#### JavaScript示例

```javascript
// 建立连接
const connection = new signalR.HubConnectionBuilder()
    .withUrl("/hubs/apStatus")  // 注意：正确的路由是 /hubs/apStatus
    .withAutomaticReconnect()
    .build();

// 监听AP状态更新
connection.on("APStatusUpdated", function (statusUpdates) {
    console.log("收到AP状态更新：", statusUpdates);
    // 处理AP状态更新逻辑
    statusUpdates.forEach(ap => {
        console.log(`AP ${ap.ApName} (${ap.MacAddress}) 状态: ${ap.Status}`);
    });
});

// 监听设备状态更新
connection.on("DeviceStatusUpdated", function (statusUpdates) {
    console.log("收到设备状态更新：", statusUpdates);
    // 处理设备状态更新逻辑
    statusUpdates.forEach(device => {
        console.log(`设备 ${device.DeviceName} (${device.MacAddress}) 状态: ${device.Status}`);
    });
});

// 监听连接状态
connection.on("JoinedGroup", function (groupName) {
    console.log(`成功加入组: ${groupName}`);
});

connection.on("Error", function (message) {
    console.error(`错误: ${message}`);
});

// 启动连接
try {
    await connection.start();
    console.log("SignalR连接成功");
    
    // 加入AP状态监听组
    await connection.invoke("JoinAPStatusGroup");
    
    // 请求当前状态
    await connection.invoke("RequestCurrentAPStatus");
} catch (err) {
    console.error("SignalR连接失败:", err);
}
```

#### C# 客户端示例

```csharp
using Microsoft.AspNetCore.SignalR.Client;

var connection = new HubConnectionBuilder()
    .WithUrl("https://your-server/hubs/apStatus")
    .WithAutomaticReconnect()
    .Build();

// 监听AP状态更新
connection.On<List<object>>("APStatusUpdated", (statusUpdates) =>
{
    Console.WriteLine($"收到AP状态更新，数量：{statusUpdates.Count}");
    // 处理AP状态更新
});

// 监听设备状态更新
connection.On<List<object>>("DeviceStatusUpdated", (statusUpdates) =>
{
    Console.WriteLine($"收到设备状态更新，数量：{statusUpdates.Count}");
    // 处理设备状态更新
});

// 监听组操作结果
connection.On<string>("JoinedGroup", (groupName) =>
{
    Console.WriteLine($"成功加入组: {groupName}");
});

connection.On<string>("Error", (message) =>
{
    Console.WriteLine($"错误: {message}");
});

try
{
    // 启动连接
    await connection.StartAsync();
    Console.WriteLine("SignalR连接成功");
    
    // 加入组
    await connection.InvokeAsync("JoinAPStatusGroup");
    
    // 请求当前状态
    await connection.InvokeAsync("RequestCurrentAPStatus");
}
catch (Exception ex)
{
    Console.WriteLine($"SignalR连接失败: {ex.Message}");
}
```

### 3. 数据格式

#### AP状态更新数据格式 (APStatusUpdated)

```json
[
  {
    "MacAddress": "00:11:22:33:44:55",
    "ApName": "AP-001",
    "Status": 1,
    "IpAddress": "*************",
    "FirmwareVersion": "1.0.0",
    "UpdateTime": "2024-01-01T12:00:00"
  }
]
```

#### 设备状态更新数据格式 (DeviceStatusUpdated)

```json
[
  {
    "MacAddress": "AA:BB:CC:DD:EE:FF",
    "DeviceName": "桌牌设备-001",
    "Status": 1,
    "BatteryLevel": 85,
    "SignalStrength": -45,
    "UpdateTime": "2024-01-01T12:00:00"
  }
]
```

#### 状态值说明

**AP状态：**
- `1` - 在线
- `2` - 离线
- `3` - 异常

**设备状态：**
- `1` - 在线
- `2` - 离线
- `3` - 异常
- `null` - 未知状态

#### 其他字段说明

- `BatteryLevel`: 电池电量百分比 (0-100)，可能为 `null`
- `SignalStrength`: 信号强度 (dBm)，负值，绝对值越小信号越强，可能为 `null`
- `UpdateTime`: 状态更新时间，ISO 8601格式

## 演示页面

访问 `/ap-status-demo.html` 可以查看实时监控演示页面，该页面展示了：

- WebSocket连接状态
- 实时AP状态卡片
- 连接控制按钮
- 操作日志

## 配置参数

### 后台同步服务配置

在 `APSyncBackgroundService.cs` 中可以配置以下参数：

```csharp
// 同步间隔时间
private readonly TimeSpan _syncInterval = TimeSpan.FromSeconds(30);

// 超时设置
using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(25));

// 分页大小
const int pageSize = 50;

// 数据库连接重试配置
private readonly int _maxRetryAttempts = 3; // 最大重试次数
private readonly int _baseDelayMs = 100; // 基础延迟时间（毫秒）
```

### SignalR 配置

在 `Admin.NET.Web.Core/Startup.cs` 中可以配置以下参数：

```csharp
// 即时通讯
services.AddSignalR(options =>
{
    options.EnableDetailedErrors = true;
    options.KeepAliveInterval = TimeSpan.FromSeconds(15); // 服务器端向客户端ping的间隔
    options.ClientTimeoutInterval = TimeSpan.FromSeconds(30); // 客户端向服务器端ping的间隔
    options.MaximumReceiveMessageSize = 1024 * 1014 * 10; // 数据包大小10M
});
```

### 数据库连接配置

系统已内置数据库连接重试机制，用于处理并发访问时的连接问题：

- **重试次数**: 最多3次重试
- **延迟策略**: 递增延迟 (100ms, 200ms, 300ms)
- **适用场景**: `SqlSugarException: Cannot Open when State is Connecting` 错误
- **作用域隔离**: 每次数据库操作使用独立的服务作用域

## 日志记录

系统提供详细的日志记录，包括：

- 连接状态变化
- 同步操作进度
- 错误和异常信息
- 性能统计数据

日志级别：
- `Information` - 重要操作和统计信息
- `Debug` - 详细的调试信息
- `Warning` - 警告信息（如超时）
- `Error` - 错误和异常

## 性能优化

1. **批量更新**：使用 `UpdateRangeAsync` 批量更新数据库
2. **分页获取**：分页获取第三方数据，避免内存溢出
3. **增量同步**：只更新有变化的AP记录
4. **连接复用**：SignalR自动重连机制
5. **内存优化**：及时释放资源，使用 `using` 语句

## 故障排除

### 常见问题

1. **SignalR连接失败**
   - 检查Hub路由是否正确配置 (`/hubs/apStatus`)
   - 确认防火墙设置
   - 检查CORS配置
   - 验证SignalR服务是否正确注册

2. **数据库连接错误**
   - **问题**: `SqlSugarException: Cannot Open when State is Connecting`
   - **原因**: 并发访问导致的数据库连接状态冲突
   - **解决方案**: 系统已内置重试机制，自动处理此类错误
   - **监控**: 查看日志中的重试记录和最终执行结果

3. **数据同步延迟**
   - 检查网络连接
   - 调整同步间隔 (默认30秒)
   - 查看日志文件
   - 检查数据库性能

4. **内存占用过高**
   - 检查是否有内存泄漏
   - 调整批量处理大小
   - 监控垃圾回收
   - 检查服务作用域是否正确释放

### 调试建议

1. **启用详细日志记录**
   ```csharp
   _logger.LogInformation("开始同步AP状态，当前时间：{Time}", DateTime.Now);
   ```

2. **监控数据库连接**
   - 查看重试机制日志
   - 监控数据库连接池状态
   - 检查并发访问模式

3. **SignalR连接监控**
   - 使用浏览器开发者工具检查WebSocket连接
   - 监控Hub连接和断开事件
   - 检查客户端重连机制

4. **性能分析**
   - 使用性能分析工具
   - 监控同步服务执行时间
   - 分析批量操作效率

## 扩展功能

### 自定义过滤器

可以在同步逻辑中添加自定义过滤器：

```csharp
// 只同步特定状态的AP
var filteredAPs = thirdPartyAPs.Where(ap => 
    ap.Status == "online" || ap.Status == "offline").ToList();
```

### 自定义通知

可以扩展Hub方法，支持更多类型的通知：

```csharp
public async Task NotifyAPAlert(string macAddress, string alertMessage)
{
    await Clients.All.SendAsync("APAlert", macAddress, alertMessage);
}
```

### 权限控制

可以添加权限验证：

```csharp
[Authorize(Roles = "Admin")]
public class APStatusHub : Hub<IAPStatusHub>
{
    // Hub方法
}
```

## 技术栈

- **.NET 8.0+**: 后台服务框架
- **SignalR**: 实时通信
- **SqlSugar**: ORM框架
- **Serilog**: 日志记录
- **Admin.NET**: 基础框架
- **IHostedService**: 后台服务托管
- **IServiceScope**: 服务作用域管理

## 版本要求

- .NET 8.0 或更高版本
- SignalR 8.0+
- SqlSugar 5.1+
- 支持WebSocket的现代浏览器
- 推荐使用最新版本的浏览器以获得最佳性能

## 更新日志

### v1.1.0 (最新)
- ✅ 修复数据库并发连接问题
- ✅ 增加数据库连接重试机制
- ✅ 优化服务作用域管理
- ✅ 增加设备状态同步功能
- ✅ 完善错误处理和日志记录
- ✅ 更新文档和使用说明

### v1.0.0
- 🎉 初始版本发布
- ✅ AP状态实时同步
- ✅ SignalR WebSocket通信
- ✅ 后台定时同步服务

---

如有问题或建议，请联系开发团队。