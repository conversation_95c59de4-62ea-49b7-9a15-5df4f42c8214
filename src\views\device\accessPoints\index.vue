<script lang="ts" setup name="accessPoints">
import { ref, reactive, onMounted, onUnmounted } from "vue";
import { auth } from '/@/utils/authFunction';
import { ElMessageBox, ElMessage } from "element-plus";
import { downloadStreamFile } from "/@/utils/download";
import { useAccessPointsApi } from '/@/api/device/accessPoints';
import editDialog from '/@/views/device/accessPoints/component/editDialog.vue'
import commandDialog from '/@/views/device/accessPoints/component/commandDialog.vue'
import printDialog from '/@/views/system/print/component/hiprint/preview.vue'
import ModifyRecord from '/@/components/table/modifyRecord.vue';
import * as signalR from '@microsoft/signalr';

const accessPointsApi = useAccessPointsApi();
const printDialogRef = ref();
const editDialogRef = ref();
const commandDialogRef = ref();
const connection = ref<signalR.HubConnection | null>(null);
const state = reactive({
  exportLoading: false,
  tableLoading: false,
  syncLoading: false, // 添加同步加载状态
  lastSyncTime: '', // 上次同步时间
  stores: {},
  showAdvanceQueryUI: false,
  dropdownData: {} as any,
  selectData: [] as any[],
  tableQueryParams: {} as any,
  tableParams: {
    page: 1,
    pageSize: 20,
    total: 0,
    field: 'createTime', // 默认的排序字段
    order: 'descending', // 排序方向
    descStr: 'descending', // 降序排序的关键字符
  },
  tableData: [],
  connectionStatus: 'disconnected', // SignalR连接状态
});

// 初始化SignalR连接
const initSignalRConnection = async () => {
  try {
    // 创建SignalR连接
    const signalRUrl = `${window.location.protocol}//${window.location.host}/hubs/apstatus`;
    console.log(signalRUrl, 'SignalR连接地址');
    connection.value = new signalR.HubConnectionBuilder()
      .withUrl(signalRUrl)
      .withAutomaticReconnect()
      .build();

    // 监听AP状态更新
    connection.value.on("APStatusUpdated", (statusUpdates: any) => {
      console.log("收到AP状态更新：", statusUpdates);
      updateTableData(statusUpdates);
    });

    // 监听连接状态变化
    connection.value.onreconnecting(() => {
      state.connectionStatus = 'reconnecting';
      console.log('SignalR正在重连...');
    });

    connection.value.onreconnected(() => {
      state.connectionStatus = 'connected';
      console.log('SignalR重连成功');
      // 重连后重新加入组
      joinAPStatusGroup();
    });

    connection.value.onclose(() => {
      state.connectionStatus = 'disconnected';
      console.log('SignalR连接已断开');
    });

    // 启动连接
    await connection.value.start();
    state.connectionStatus = 'connected';
    console.log('SignalR连接已建立');

    // 加入AP状态组
    await joinAPStatusGroup();

    // 请求当前状态
    await requestCurrentAPStatus();

  } catch (error) {
    console.error('SignalR连接失败:', error);
    state.connectionStatus = 'error';
  }
};

// 加入AP状态组
const joinAPStatusGroup = async () => {
  if (connection.value && state.connectionStatus === 'connected') {
    try {
      // 传递组名参数，通常是"APStatus"或类似的组标识
      await connection.value.invoke("JoinAPStatusGroup", "APStatus");
      console.log('已加入AP状态组');
    } catch (error) {
      console.error('加入AP状态组失败:', error);
    }
  }
};

// 请求当前AP状态
const requestCurrentAPStatus = async () => {
  if (connection.value && state.connectionStatus === 'connected') {
    try {
      await connection.value.invoke("RequestCurrentAPStatus");
      console.log('已请求当前AP状态');
    } catch (error) {
      console.error('请求当前AP状态失败:', error);
    }
  }
};

// 获取信号强度颜色
const getSignalColor = (strength: number) => {
  if (!strength) return '#909399';
  if (strength >= -50) return '#67C23A'; // 强信号 - 绿色
  if (strength >= -70) return '#E6A23C'; // 中等信号 - 橙色
  return '#F56C6C'; // 弱信号 - 红色
};

// 更新表格数据
const updateTableData = (statusUpdates: any) => {
  if (!Array.isArray(statusUpdates)) {
    statusUpdates = [statusUpdates];
  }

  statusUpdates.forEach((update: any) => {
    const index = state.tableData.findIndex((item: any) => 
      item.id === update.id || 
      item.mac_address === update.mac_address || 
      item.ap_name === update.ap_name
    );

    if (index !== -1) {
      // 更新现有记录的实时字段
      const currentItem = state.tableData[index];
      let hasUpdate = false;
      
      if (update.ip_address !== undefined && currentItem.ip_address !== update.ip_address) {
        currentItem.ip_address = update.ip_address;
        hasUpdate = true;
      }
      if (update.ap_status !== undefined && currentItem.ap_status !== update.ap_status) {
        currentItem.ap_status = update.ap_status;
        hasUpdate = true;
      }
      if (update.firmware_version !== undefined && currentItem.firmware_version !== update.firmware_version) {
        currentItem.firmware_version = update.firmware_version;
        hasUpdate = true;
      }
      if (update.signal_strength !== undefined && currentItem.signal_strength !== update.signal_strength) {
        currentItem.signal_strength = update.signal_strength;
        hasUpdate = true;
      }
      if (update.connected_devices_count !== undefined && currentItem.connected_devices_count !== update.connected_devices_count) {
        currentItem.connected_devices_count = update.connected_devices_count;
        hasUpdate = true;
      }
      
      if (hasUpdate) {
        // 添加更新标记用于动画效果
        currentItem._updated = true;
        // 触发响应式更新
        state.tableData[index] = { ...currentItem };
        
        // 2秒后移除更新标记
        setTimeout(() => {
          if (state.tableData[index]) {
            state.tableData[index]._updated = false;
          }
        }, 2000);
        
        console.log(`AP设备 ${currentItem.ap_name || currentItem.mac_address} 状态已更新`);
      }
    }
  });
};

// 页面加载时
onMounted(async () => {
  // 获取上次同步时间
  const lastSync = localStorage.getItem('accessPoints_lastSyncTime');
  if (lastSync) {
    state.lastSyncTime = lastSync;
  }
  
  // 初始化SignalR连接
  await initSignalRConnection();
});

// 页面卸载时
onUnmounted(() => {
  if (connection.value) {
    connection.value.stop();
    console.log('SignalR连接已关闭');
  }
});

// 查询操作
const handleQuery = async (params: any = {}) => {
  state.tableLoading = true;
  state.tableParams = Object.assign(state.tableParams, params);
  const result = await accessPointsApi.page(Object.assign(state.tableQueryParams, state.tableParams)).then(res => res.data.result);
  state.tableParams.total = result?.total;
  state.tableData = result?.items ?? [];
  state.tableLoading = false;
};
//同步第三方设备
const syncData = async () => {
  // 防止重复点击
  if (state.syncLoading) {
    return;
  }
  
  // 显示确认对话框
  ElMessageBox.confirm(
    '确定要同步第三方设备数据吗？此操作将从第三方系统获取最新的设备信息并更新到本地数据库。',
    '确认同步',
    {
      confirmButtonText: '确定同步',
      cancelButtonText: '取消',
      type: 'info',
      distinguishCancelAndClose: true,
    }
  ).then(async () => {
    try {
      // 显示加载状态
      state.syncLoading = true;
      ElMessage.info('正在同步第三方设备数据，请稍候...');
      
      // 调用同步API
      const response = await accessPointsApi.syncData({});
      const result = response.data?.result;
      
      // 同步成功后刷新数据
      await handleQuery();
      
      // 记录同步时间
      const currentTime = new Date().toLocaleString('zh-CN');
      state.lastSyncTime = currentTime;
      localStorage.setItem('accessPoints_lastSyncTime', currentTime);
      
      // 显示成功消息
      if (result && typeof result === 'object') {
        // 如果返回的是详细统计信息
        if ('createdCount' in result || 'updatedCount' in result) {
          const createdCount = result.createdCount || 0;
          const updatedCount = result.updatedCount || 0;
          const skippedCount = result.skippedCount || 0;
          const errorCount = result.errorCount || 0;
          ElMessage.success(`同步完成：新增 ${createdCount} 个，更新 ${updatedCount} 个，跳过 ${skippedCount} 个，错误 ${errorCount} 个`);
        } else if (result.message) {
          ElMessage.success(`同步完成：${result.message}`);
        } else {
          ElMessage.success('同步完成！');
        }
      } else if (typeof result === 'number') {
        // 如果返回的是数量
        ElMessage.success(`同步成功！共同步了 ${result} 条设备数据`);
      } else {
        ElMessage.success('同步完成！');
      }
    } catch (error: any) {
      // 错误处理
      console.error('同步失败:', error);
      ElMessage.error(error.message || '同步失败，请检查网络连接或联系管理员');
    } finally {
      // 确保加载状态被清除
      state.syncLoading = false;
    }
  }).catch((action) => {
    // 用户取消操作
    if (action === 'cancel') {
      ElMessage.info('已取消同步操作');
    }
  });
}

// 列排序
const sortChange = async (column: any) => {
  state.tableParams.field = column.prop;
  state.tableParams.order = column.order;
  await handleQuery();
};

// 删除
const delAccessPoints = (row: any) => {
  ElMessageBox.confirm(`确定要删除吗?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    await accessPointsApi.delete({ id: row.id });
    handleQuery();
    ElMessage.success("删除成功");
  }).catch(() => {});
};

// 批量删除
const batchDelAccessPoints = () => {
  ElMessageBox.confirm(`确定要删除${state.selectData.length}条记录吗?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    await accessPointsApi.batchDelete(state.selectData.map(u => ({ id: u.id }) )).then(res => {
      ElMessage.success(`成功批量删除${res.data.result}条记录`);
      handleQuery();
    });
  }).catch(() => {});
};

handleQuery();
</script>
<template>
  <div class="accessPoints-container" v-loading="state.exportLoading">
    <!-- 查询区域 -->
    <el-card shadow="hover" class="query-card">
      <el-form :model="state.tableQueryParams" ref="queryForm" labelWidth="90">
        <el-row :gutter="16">
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4" class="mb10">
            <el-form-item label="关键字">
              <el-input v-model="state.tableQueryParams.keyword" clearable placeholder="请输入模糊查询关键字" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="AP名称">
              <el-input v-model="state.tableQueryParams.ap_name" clearable placeholder="请输入AP名称" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="AP地址">
              <el-input v-model="state.tableQueryParams.ap_location" clearable placeholder="请输入AP地址" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="AP状态">
              <el-select v-model="state.tableQueryParams.ap_status" clearable placeholder="请选择AP状态">
                <el-option label="在线" :value="0" />
                <el-option label="离线" :value="1" />
                <el-option label="异常" :value="2" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <!-- 操作按钮区域 -->
        <el-row class="operation-row">
          <el-col :span="24">
            <div class="operation-container">
              <div class="button-group">
                <el-button type="primary" icon="ele-Search" @click="handleQuery" v-auth="'accessPoints:page'"
                  v-reclick="1000"> 查询 </el-button>
                <el-button icon="ele-Refresh" @click="() => state.tableQueryParams = {}"> 重置 </el-button>
                <el-button icon="ele-ZoomIn" @click="() => state.showAdvanceQueryUI = true"
                  v-if="!state.showAdvanceQueryUI"> 高级查询 </el-button>
                <el-button icon="ele-ZoomOut" @click="() => state.showAdvanceQueryUI = false"
                  v-if="state.showAdvanceQueryUI"> 隐藏 </el-button>
              </div>
              
              <div class="action-group">
                <el-button type="success" icon="ele-Plus"
                  @click="editDialogRef.openDialog(null, '新增接入点/网关表')" v-auth="'accessPoints:add'"> 新增 </el-button>
                <el-button type="warning" 
                   :icon="state.syncLoading ? 'ele-Loading' : 'ele-Refresh'" 
                   @click="syncData" 
                   v-auth="'accessPoints:page'"
                   :loading="state.syncLoading"
                   :disabled="state.syncLoading"
                   v-reclick="1000"> 
                   {{ state.syncLoading ? '同步中...' : '同步数据' }}
                 </el-button>
                <el-button type="danger" icon="ele-Delete" @click="batchDelAccessPoints"
                  :disabled="state.selectData.length == 0" v-auth="'accessPoints:batchDelete'"> 
                  删除({{ state.selectData.length }})
                </el-button>
              </div>
            </div>
          </el-col>
        </el-row>
        
        <!-- 状态信息区域 -->
        <el-row class="status-row">
          <el-col :span="24">
            <div class="status-container">
              <div class="sync-info" v-if="state.lastSyncTime">
                <el-icon><ele-Clock /></el-icon>
                <span>上次同步: {{ state.lastSyncTime }}</span>
              </div>
              <div class="connection-status" 
                   :class="`status-${state.connectionStatus}`">
                <el-icon><ele-Connection /></el-icon>
                <span>实时状态: {{ 
                  state.connectionStatus === 'connected' ? '已连接' : 
                  state.connectionStatus === 'reconnecting' ? '重连中' : 
                  state.connectionStatus === 'error' ? '连接错误' : '未连接' 
                }}</span>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <!-- 数据表格区域 -->
    <el-card class="table-card" shadow="hover">
      <template #header>
        <div class="table-header">
          <span class="table-title">
            <el-icon><ele-List /></el-icon>
            AP设备列表
          </span>
          <div class="table-stats">
            <el-tag size="small" type="info">共 {{ state.tableParams.total }} 条记录</el-tag>
            <el-tag size="small" type="success" v-if="state.selectData.length > 0">
              已选择 {{ state.selectData.length }} 条
            </el-tag>
          </div>
        </div>
      </template>
      
      <el-table :data="state.tableData" @selection-change="(val: any[]) => { state.selectData = val; }"
        style="width: 100%" v-loading="state.tableLoading" tooltip-effect="light" row-key="id" @sort-change="sortChange"
        border stripe>
        <el-table-column type="selection" width="50" align="center"
          v-if="auth('accessPoints:batchDelete') || auth('accessPoints:export')" />
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column prop='ap_name' label='AP名称' min-width="120" show-overflow-tooltip sortable />
        <el-table-column prop='mac_address' label='物理地址' width="140" show-overflow-tooltip />
        <el-table-column prop='ip_address' label='IP地址' width="130" show-overflow-tooltip>
          <template #default="scope">
            <span :class="{ 'field-updated': scope.row._updated }">
              {{ scope.row.ip_address || '-' }}
            </span>
          </template>
        </el-table-column>
        <!-- <el-table-column prop='ap_location' label='AP地址' min-width="150" show-overflow-tooltip /> -->
        <el-table-column prop='ap_status' label='状态' width="80" align="center">
           <template #default="scope">
             <el-tag 
               :type="scope.row.ap_status === 0 ? 'success' : scope.row.ap_status === 1 ? 'danger' : 'warning'"
               size="small"
               effect="dark"
               :class="{ 'field-updated': scope.row._updated }">
               {{ scope.row.ap_status === 0 ? '在线' : scope.row.ap_status ===1 ? '离线' : '异常' }}
             </el-tag>
           </template>
         </el-table-column>
        <el-table-column prop='firmware_version' label='固件版本' width="110" show-overflow-tooltip>
          <template #default="scope">
            <span :class="{ 'field-updated': scope.row._updated }">
              {{ scope.row.firmware_version || '-' }}
            </span>
          </template>
        </el-table-column>
        <!-- <el-table-column prop='signal_strength' label='信号强度' width="100" align="center">
           <template #default="scope">
             <span :style="{ color: getSignalColor(scope.row.signal_strength), fontWeight: 'bold' }"
                   :class="{ 'field-updated': scope.row._updated }">
               {{ scope.row.signal_strength ? `${scope.row.signal_strength}dBm` : '-' }}
             </span>
           </template>
         </el-table-column> -->
        <!-- <el-table-column prop='connected_devices_count' label='连接数' width="80" align="center">
          <template #default="scope">
            <el-badge :value="scope.row.connected_devices_count || 0" :max="999" type="primary">
              <span :class="{ 'field-updated': scope.row._updated }">
                {{ scope.row.connected_devices_count || 0 }}
              </span>
            </el-badge>
          </template>
        </el-table-column>
        <el-table-column prop='max_devices' label='最大连接' width="90" align="center" show-overflow-tooltip /> -->
        <el-table-column label="修改记录" width="100" align="center">
          <template #default="scope">
            <ModifyRecord :data="scope.row" />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" align="center" fixed="right"
          v-if="auth('accessPoints:update') || auth('accessPoints:delete')">
          <template #default="scope">
            <el-button icon="ele-Edit" size="small" text type="primary"
              @click="editDialogRef.openDialog(scope.row, '编辑接入点/网关表')" v-auth="'accessPoints:update'"> 
              编辑 
            </el-button>
            <el-button icon="ele-Edit" size="small" text type="primary" 
              @click="commandDialogRef.openDialog(scope.row, '命令行')" v-auth="'accessPoints:update'"> 
               命令行
            </el-button>
            <el-button icon="ele-Delete" size="small" text type="danger" @click="delAccessPoints(scope.row)"
              v-auth="'accessPoints:delete'"> 
              删除 
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination v-model:currentPage="state.tableParams.page" v-model:page-size="state.tableParams.pageSize"
        @size-change="(val: any) => handleQuery({ pageSize: val })"
        @current-change="(val: any) => handleQuery({ page: val })" layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10, 20, 50, 100, 200, 500]" :total="state.tableParams.total" size="small" background />
      <printDialog ref="printDialogRef" :title="'打印接入点/网关表'" @reloadTable="handleQuery" />
      <editDialog ref="editDialogRef" @reloadTable="handleQuery" />
      <commandDialog ref="commandDialogRef" @reloadTable="handleQuery" />
    </el-card>
  </div>
</template>
<style scoped>
/* 容器样式 */
.accessPoints-container {
  padding: 0;
  background-color: #f5f7fa;
  min-height: calc(100vh - 84px);
}

/* 查询卡片样式 */
.query-card {
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.query-card :deep(.el-card__body) {
  padding: 20px;
}

/* 操作区域样式 */
.operation-row {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #ebeef5;
}

.operation-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
}

.button-group {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.action-group {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 状态信息区域样式 */
.status-row {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #f0f2f5;
}

.status-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.sync-info {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #909399;
  font-size: 13px;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.status-connected {
  color: #67C23A;
  background-color: rgba(103, 194, 58, 0.1);
}

.status-reconnecting {
  color: #E6A23C;
  background-color: rgba(230, 162, 60, 0.1);
}

.status-error,
.status-disconnected {
  color: #F56C6C;
  background-color: rgba(245, 108, 108, 0.1);
}

/* 表格卡片样式 */
.table-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
}

.table-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.table-stats {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 表单控件样式 */
:deep(.el-input), :deep(.el-select), :deep(.el-input-number) {
  width: 100%;
}

:deep(.el-form-item) {
  margin-bottom: 16px;
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 6px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #fafafa;
  color: #606266;
  font-weight: 600;
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background-color: #fafbfc;
}

:deep(.el-table__body tr:hover > td) {
  background-color: #f5f7fa !important;
}

/* 实时更新动画效果 */
.field-updated {
  animation: highlight 2s ease-in-out;
  position: relative;
}

@keyframes highlight {
  0% {
    background-color: #67C23A;
    color: white;
    transform: scale(1.05);
    box-shadow: 0 0 10px rgba(103, 194, 58, 0.6);
    border-radius: 4px;
  }
  50% {
    background-color: #85CE61;
    color: white;
    transform: scale(1.02);
    box-shadow: 0 0 8px rgba(103, 194, 58, 0.4);
    border-radius: 4px;
  }
  100% {
    background-color: transparent;
    color: inherit;
    transform: scale(1);
    box-shadow: none;
    border-radius: 0;
  }
}

/* 为el-tag添加特殊的更新动画 */
.el-tag.field-updated {
  animation: tag-highlight 2s ease-in-out;
}

@keyframes tag-highlight {
  0% {
    transform: scale(1.1);
    box-shadow: 0 0 15px rgba(103, 194, 58, 0.8);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 10px rgba(103, 194, 58, 0.5);
  }
  100% {
    transform: scale(1);
    box-shadow: none;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .operation-container {
    flex-direction: column;
    align-items: stretch;
  }
  
  .button-group,
  .action-group {
    justify-content: center;
  }
  
  .status-container {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
  
  .table-header {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .accessPoints-container {
    padding: 0 8px;
  }
  
  .query-card :deep(.el-card__body) {
    padding: 16px;
  }
  
  .button-group,
  .action-group {
    flex-direction: column;
    width: 100%;
  }
  
  .button-group .el-button,
  .action-group .el-button {
    width: 100%;
  }
}
</style>