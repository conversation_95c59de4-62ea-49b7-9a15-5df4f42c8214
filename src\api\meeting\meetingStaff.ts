﻿import {useBaseApi} from '/@/api/base';

// 会议人员表接口服务
export const useMeetingStaffApi = () => {
	const baseApi = useBaseApi("meetingStaff");
	return {
		// 分页查询会议人员表
		page: baseApi.page,
		// 查看会议人员表详细
		detail: baseApi.detail,
		// 新增会议人员表
		add: baseApi.add,
		// 更新会议人员表
		update: baseApi.update,
		// 删除会议人员表
		delete: baseApi.delete,
		// 批量删除会议人员表
		batchDelete: baseApi.batchDelete,
		// 导出会议人员表数据
		exportData: baseApi.exportData,
		// 导入会议人员表数据
		importData: baseApi.importData,
		// 下载会议人员表数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
	}
}

// 会议人员表实体
export interface MeetingStaff {
	// 主键Id
	id: number;
	// 平台员工ID
	staff_id: number;
	// 员工编号
	staff_code: string;
	// 员工姓名
	staff_name: string;
	// 职位
	position: string;
	// 部门
	department: string;
	// 邮箱
	email: string;
	// 手机号
	phone: string;
	// 头像
	avatar_url: string;
	// 绑定会议室ID
	meeting_room_id: number;
	// 自定义字段1
	field1: string;
	// 自定义字段2
	field2: string;
	// 自定义字段2
	field3: string;
	// 自定义字段2
	field4: string;
	// 自定义字段2
	field5: string;
	// 自定义字段2
	field6: string;
	// 描述
	description: string;
	// 1：激活；2：未激活
	meeting_staff_status: number;
	// 软删除
	isDelete?: boolean;
	// 创建时间
	createTime: string;
	// 更新时间
	updateTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
}