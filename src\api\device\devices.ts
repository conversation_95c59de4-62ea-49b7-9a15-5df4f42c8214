﻿import { useBaseApi } from '/@/api/base';
import request from '/@/utils/request';

// 蓝牙桌牌设备接口服务
export const useDevicesApi = () => {
	const baseApi = useBaseApi("devices");
	return {
		// 分页查询蓝牙桌牌设备
		page: baseApi.page,
		// 查看蓝牙桌牌设备详细
		detail: baseApi.detail,
		// 新增蓝牙桌牌设备
		add: baseApi.add,
		// 更新蓝牙桌牌设备
		update: baseApi.update,
		// 删除蓝牙桌牌设备
		delete: baseApi.delete,
		// 批量删除蓝牙桌牌设备		
		batchDelete: baseApi.batchDelete,
		//获取设备统计信息
		Statistics: function(data: any, cancel: boolean = false) {
			return request({
				url: baseApi.baseUrl + 'Statistics',
				method: 'get',
				data
			}, cancel);
		},
		//获取设备详细统计信息
		DetailedStatistics: function(data: any, cancel: boolean = false) {
			return request({
				url: baseApi.baseUrl + 'DetailedStatistics',
				method: 'get',
				data
			}, cancel);
		},
		//根据条件获取设备统计信息
		StatisticsByCondition: function(data: any, cancel: boolean = false) {
			return request({
				url: baseApi.baseUrl + 'StatisticsByCondition',
				method: 'post',
				data
			}, cancel);
		},
		//按会议室分组获取设备统计信息
		statisticsByMeetingRoom: function(data: any, cancel: boolean = false) {
			return request({
				url: baseApi.baseUrl + 'statisticsByMeetingRoom',
				method: 'post',
				data
			}, cancel);
		},
		// 从第三方同步设备
		SyncFromThirdParty: function (data: any, cancel: boolean = false) {
			return request({
				url: baseApi.baseUrl + 'SyncFromThirdParty',
				method: 'post',
				data
			}, cancel);
		}
	}
}

// 蓝牙桌牌设备实体
export interface Devices {
	// 主键Id
	id: number;
	// 网关ID
	ap_id: number;
	// 设备ID
	device_id?: string;
	// 设备名称
	device_name: string;
	// MAC地址
	mac_address: string;
	// 1：姓名桌牌；2：价格标签
	device_type: number;
	// 1：在线；2离线；3：绑定；4:异常
	status: number;
	// 电量
	battery_level: number;
	// 固件版本
	firmware_version: string;
	// 绑定租户Id
	bindTenantId: number;
	// 绑定用户Id
	bindUserId: number;
	// 软删除
	isDelete?: boolean;
	// 创建时间
	createTime: string;
	// 更新时间
	updateTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
}