﻿<script lang="ts" setup name="meetingRooms">
import { ref, reactive, onMounted } from "vue";
import { auth } from '/@/utils/authFunction';
import { ElMessageBox, ElMessage } from "element-plus";
import { downloadStreamFile } from "/@/utils/download";
import { useMeetingRoomsApi } from '/@/api/meeting/meetingRooms';
import editDialog from '/@/views/meeting/meetingRooms/component/editDialog.vue'
import printDialog from '/@/views/system/print/component/hiprint/preview.vue'
import ModifyRecord from '/@/components/table/modifyRecord.vue';

const meetingRoomsApi = useMeetingRoomsApi();
const printDialogRef = ref();
const editDialogRef = ref();
const state = reactive({
  exportLoading: false,
  tableLoading: false,
  stores: {},
  showAdvanceQueryUI: false,
  dropdownData: {} as any,
  selectData: [] as any[],
  tableQueryParams: {} as any,
  tableParams: {
    page: 1,
    pageSize: 20,
    total: 0,
    field: 'createTime', // 默认的排序字段
    order: 'descending', // 排序方向
    descStr: 'descending', // 降序排序的关键字符
  },
  tableData: [],
});

// 页面加载时
onMounted(async () => {
});

// 查询操作
const handleQuery = async (params: any = {}) => {
  state.tableLoading = true;
  state.tableParams = Object.assign(state.tableParams, params);
  const result = await meetingRoomsApi.page(Object.assign(state.tableQueryParams, state.tableParams)).then(res => res.data.result);
  state.tableParams.total = result?.total;
  state.tableData = result?.items ?? [];
  state.tableLoading = false;
};

// 列排序
const sortChange = async (column: any) => {
  state.tableParams.field = column.prop;
  state.tableParams.order = column.order;
  await handleQuery();
};

// 删除
const delMeetingRooms = (row: any) => {
  ElMessageBox.confirm(`确定要删除吗?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    await meetingRoomsApi.delete({ id: row.id });
    handleQuery();
    ElMessage.success("删除成功");
  }).catch(() => {});
};

// 批量删除
const batchDelMeetingRooms = () => {
  ElMessageBox.confirm(`确定要删除${state.selectData.length}条记录吗?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    await meetingRoomsApi.batchDelete(state.selectData.map(u => ({ id: u.id }) )).then(res => {
      ElMessage.success(`成功批量删除${res.data.result}条记录`);
      handleQuery();
    });
  }).catch(() => {});
};

// 切换启用/停用状态
const toggleStatus = async (row: any, newStatus: number) => {
  const statusText = newStatus === 1 ? '启用' : '停用';
  
  try {
    await meetingRoomsApi.update({ ...row, meeting_status: newStatus });
    row.meeting_status = newStatus;
    ElMessage.success(`${statusText}成功`);
  } catch (error) {
    ElMessage.error(`${statusText}失败`);
    // 恢复原状态
    handleQuery();
  }
};

handleQuery();
</script>
<template>
  <div class="meetingRooms-container" v-loading="state.exportLoading">
    <el-card shadow="hover" :body-style="{ paddingBottom: '0' }">
      <el-form :model="state.tableQueryParams" ref="queryForm" labelWidth="90">
        <el-row>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10">
            <el-form-item label="关键字">
              <el-input v-model="state.tableQueryParams.keyword" clearable placeholder="请输入模糊查询关键字" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="房间名称">
              <el-input v-model="state.tableQueryParams.room_name" clearable placeholder="请输入房间名称" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="房间编号">
              <el-input v-model="state.tableQueryParams.room_code" clearable placeholder="请输入房间编号" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="房间位置">
              <el-input v-model="state.tableQueryParams.room_location" clearable placeholder="请输入房间位置" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="会议室容量">
              <el-input-number v-model="state.tableQueryParams.capacity" clearable placeholder="请输入会议室容量" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="绑定设备">
              <el-input v-model="state.tableQueryParams.equipment" clearable placeholder="请输入绑定设备" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="会议室状态">
              <el-input-number v-model="state.tableQueryParams.meeting_status" clearable placeholder="请输入会议室状态" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10">
            <el-form-item>
              <el-button-group style="display: flex; align-items: center;">
                <el-button type="primary" icon="ele-Search" @click="handleQuery" v-auth="'meetingRooms:page'"
                  v-reclick="1000"> 查询 </el-button>
                <el-button icon="ele-Refresh" @click="() => state.tableQueryParams = {}"> 重置 </el-button>
                <!--<el-button icon="ele-ZoomIn" @click="() => state.showAdvanceQueryUI = true" v-if="!state.showAdvanceQueryUI" style="margin-left:5px;"> 高级查询 </el-button>
                <el-button icon="ele-ZoomOut" @click="() => state.showAdvanceQueryUI = false" v-if="state.showAdvanceQueryUI" style="margin-left:5px;"> 隐藏 </el-button>-->
                <el-button type="danger" style="margin-left:5px;" icon="ele-Delete" @click="batchDelMeetingRooms"
                  :disabled="state.selectData.length == 0" v-auth="'meetingRooms:batchDelete'"> 删除 </el-button>
                <el-button type="primary" style="margin-left:5px;" icon="ele-Plus"
                  @click="editDialogRef.openDialog(null, '新增会议室表')" v-auth="'meetingRooms:add'"> 新增 </el-button>
              </el-button-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <el-card class="full-table" shadow="hover" style="margin-top: 5px">
      <el-table :data="state.tableData" @selection-change="(val: any[]) => { state.selectData = val; }"
        style="width: 100%" v-loading="state.tableLoading" tooltip-effect="light" row-key="id" @sort-change="sortChange"
        border>
        <el-table-column type="selection" width="40" align="center"
          v-if="auth('meetingRooms:batchDelete') || auth('meetingRooms:export')" />
        <el-table-column type="index" label="序号" width="55" align="center" />
        <el-table-column prop='room_name' label='房间名称' show-overflow-tooltip />
        <el-table-column prop='room_code' label='房间编号' show-overflow-tooltip />
        <el-table-column prop='room_location' label='房间位置' show-overflow-tooltip />
        <el-table-column prop='capacity' label='会议室容量' show-overflow-tooltip />
        <!-- <el-table-column prop='equipment' label='绑定设备' show-overflow-tooltip /> -->
        <el-table-column prop='meeting_status' label='会议室状态' show-overflow-tooltip>
          <template #default="scope">
            <el-tag v-if="scope.row.meeting_status === 1" type="success">可用</el-tag>
            <el-tag v-else-if="scope.row.meeting_status === 2" type="danger">占用</el-tag>
            <el-tag v-else-if="scope.row.meeting_status === 3" type="warning">维护</el-tag>
            <el-tag v-else-if="scope.row.meeting_status === 4" type="info">停用</el-tag>
            <el-tag v-else type="info">未知</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="修改记录" width="100" align="center" show-overflow-tooltip>
          <template #default="scope">
            <ModifyRecord :data="scope.row" />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250" align="center" fixed="right" show-overflow-tooltip
          v-if="auth('meetingRooms:update') || auth('meetingRooms:delete')">
          <template #default="scope">
            <el-switch
              v-if="scope.row.meeting_status === 1 || scope.row.meeting_status === 4"
              :model-value="scope.row.meeting_status"
              :active-value="1"
              :inactive-value="4"
              active-text="启用"
              inactive-text="停用"
              size="small"
              @change="(val: number) => toggleStatus(scope.row, val)"
              v-auth="'meetingRooms:update'"
              style="margin: 0 5px;"
            />
            <el-button icon="ele-Edit" size="small" text type="primary"
              @click="editDialogRef.openDialog(scope.row, '编辑会议室表')" v-auth="'meetingRooms:update'"> 编辑 </el-button> 
            <el-button icon="ele-Delete" size="small" text type="primary" @click="delMeetingRooms(scope.row)"
              v-auth="'meetingRooms:delete'"> 删除 </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination v-model:currentPage="state.tableParams.page" v-model:page-size="state.tableParams.pageSize"
        @size-change="(val: any) => handleQuery({ pageSize: val })"
        @current-change="(val: any) => handleQuery({ page: val })" layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10, 20, 50, 100, 200, 500]" :total="state.tableParams.total" size="small" background />
      <printDialog ref="printDialogRef" :title="'打印会议室表'" @reloadTable="handleQuery" />
      <editDialog ref="editDialogRef" @reloadTable="handleQuery" />
    </el-card>
  </div>
</template>
<style scoped>
:deep(.el-input), :deep(.el-select), :deep(.el-input-number) {
  width: 100%;
}
</style>