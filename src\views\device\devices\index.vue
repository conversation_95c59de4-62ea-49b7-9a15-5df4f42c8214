<script lang="ts" setup name="devices">
import { ref, reactive, onMounted } from "vue";
import { auth } from '/@/utils/authFunction';
import { ElMessageBox, ElMessage } from "element-plus";
import { downloadStreamFile } from "/@/utils/download";
import { useDevicesApi } from '/@/api/device/devices';
import editDialog from '/@/views/device/devices/component/editDialog.vue'
import printDialog from '/@/views/system/print/component/hiprint/preview.vue'
import ModifyRecord from '/@/components/table/modifyRecord.vue';

const devicesApi = useDevicesApi();
const printDialogRef = ref();
const editDialogRef = ref();
const state = reactive({
  exportLoading: false,
  tableLoading: false,
  roomLoading: false,
  stores: {},
  showAdvanceQueryUI: false,
  dropdownData: {} as any,
  selectData: [] as any[],
  selectedRoom: null as any,
  tableQueryParams: {} as any,
  tableParams: {
    page: 1,
    pageSize: 20,
    total: 3,
    field: 'createTime', // 默认的排序字段
    order: 'descending', // 排序方向
    descStr: 'descending', // 降序排序的关键字符
  },
  tableData: [
    {
      id: 1,
      serialNumber: '46C101012287',
      seat: '座位1',
      meetingRoom: '会议室01',
      workstation: '工位001',
      category: '1',
      result: '已连接',
      signal: 'strong',
      gatewayMac: '65:C4:D8:81:50:32',
      battery: 85
    },
    {
      id: 2,
      serialNumber: '46C101012288',
      seat: '座位2',
      meetingRoom: '会议室01',
      workstation: '工位002',
      category: '2',
      result: '已连接',
      signal: 'medium',
      gatewayMac: '65:C4:D8:81:50:33',
      battery: 65
    },
    {
      id: 3,
      serialNumber: '46C101012289',
      seat: '座位3',
      meetingRoom: '会议室02',
      workstation: '工位003',
      category: '1',
      result: '连接中',
      signal: 'weak',
      gatewayMac: '65:C4:D8:81:50:34',
      battery: 25
    }
  ],
  roomList: [
    { id: 1, name: '会议室01', deviceCount: 5, status: 'online' },
    { id: 2, name: '会议室02', deviceCount: 3, status: 'offline' },
    { id: 3, name: '会议室03', deviceCount: 8, status: 'online' },
    { id: 4, name: '会议室04', deviceCount: 2, status: 'online' },
    { id: 5, name: '会议室05', deviceCount: 6, status: 'offline' },
    { id: 6, name: '会议室06', deviceCount: 4, status: 'online' },
    { id: 7, name: '会议室07', deviceCount: 7, status: 'offline' },
    { id: 8, name: '会议室08', deviceCount: 3, status: 'online' },
    { id: 9, name: '会议室09', deviceCount: 9, status: 'online' },
    { id: 10, name: '会议室10', deviceCount: 2, status: 'offline' },
    { id: 11, name: '会议室11', deviceCount: 5, status: 'online' },
    { id: 12, name: '会议室12', deviceCount: 6, status: 'online' },
    { id: 13, name: '会议室13', deviceCount: 4, status: 'offline' },
    { id: 14, name: '会议室14', deviceCount: 8, status: 'online' },
    { id: 15, name: '会议室15', deviceCount: 3, status: 'offline' },
  ],
});

// 页面加载时
onMounted(async () => {
  // 默认选择“所有会议室”
  state.selectedRoom = { id: null, name: '所有会议室' };
  handleQuery();
});

// 选择会议室
const selectRoom = (room: any) => {
  state.selectedRoom = room;
  // 如果选择的是“所有会议室”，则不按 roomId 筛选
  if (room.id === null) {
    delete state.tableQueryParams.roomId;
  } else {
    state.tableQueryParams.roomId = room.id;
  }
  handleQuery();
};

// 查询操作
const handleQuery = async (params: any = {}) => {
  state.tableLoading = true;
  state.tableParams = Object.assign(state.tableParams, params);
  const queryParams = Object.assign({}, state.tableQueryParams, state.tableParams);
  if (state.selectedRoom) {
    queryParams.roomId = state.selectedRoom.id;
  }
  const result = await devicesApi.page(queryParams).then(res => res.data.result);
  state.tableParams.total = result?.total;
  state.tableData = result?.items ?? [];
  state.tableLoading = false;
};

// 列排序
const sortChange = async (column: any) => {
  state.tableParams.field = column.prop;
  state.tableParams.order = column.order;
  await handleQuery();
};

// 删除
const delDevices = (row: any) => {
  ElMessageBox.confirm(`确定要删除吗?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    await devicesApi.delete({ id: row.id });
    handleQuery();
    ElMessage.success("删除成功");
  }).catch(() => {});
};

// 批量删除
const batchDelDevices = () => {
  ElMessageBox.confirm(`确定要删除${state.selectData.length}条记录吗?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    await devicesApi.batchDelete(state.selectData.map(u => ({ id: u.id }) )).then(res => {
      ElMessage.success(`成功批量删除${res.data.result}条记录`);
      handleQuery();
    });
  }).catch(() => {});
};

handleQuery();
</script>
<template>
  <div class="devices-container" v-loading="state.exportLoading">
    <div class="layout-container">
      <!-- 左侧会议室列表 -->
      <div class="left-panel">
        <el-card shadow="hover" class="room-card">
          <template #header>
            <div class="card-header">
              <span>会议室</span>
            </div>
          </template>
          <div class="room-list" v-loading="state.roomLoading">
            <!-- 新增“所有会议室”选项 -->
            <div class="room-item" :class="{ active: state.selectedRoom?.id === null }"
              @click="selectRoom({ id: null, name: '所有会议室' })">
              <div class="room-content">
                <div class="room-name">所有会议室</div>
              </div>
            </div>
            <div v-for="room in state.roomList" :key="room.id" class="room-item"
              :class="{ active: state.selectedRoom?.id === room.id }" @click="selectRoom(room)">
              <div class="room-content">
                <div class="room-name">{{ room.name }}</div>
                <div class="room-info">
                  <span class="device-count">设备数: {{ room.deviceCount }}</span>
                  <span class="status" :class="room.status">{{ room.status === 'online' ? '在线' : '离线' }}</span>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 右侧内容区域 -->
      <div class="right-panel">
        <!-- 右上：查询条件和按钮 -->
        <el-card shadow="hover" class="query-card">
          <el-form :model="state.tableQueryParams" ref="queryForm" labelWidth="80px" class="query-form">
            <!-- 查询条件行 -->
            <el-row :gutter="16" class="query-row">
              <el-col :xl="4" :lg="6" :md="8" :sm="12" :xs="24">
                <el-form-item label="编号" class="compact-form-item">
                  <el-input v-model="state.tableQueryParams.serialNumber" placeholder="请输入编号" clearable size="default" />
                </el-form-item>
              </el-col>
              <el-col :xl="4" :lg="6" :md="8" :sm="12" :xs="24">
                <el-form-item label="座位" class="compact-form-item">
                  <el-input v-model="state.tableQueryParams.seat" placeholder="请输入座位" clearable size="default" />
                </el-form-item>
              </el-col>
              <el-col :xl="4" :lg="6" :md="8" :sm="12" :xs="24">
                <el-form-item label="工位号" class="compact-form-item">
                  <el-input v-model="state.tableQueryParams.workstation" placeholder="请输入工位号" clearable size="default" />
                </el-form-item>
              </el-col>
              <el-col :xl="4" :lg="6" :md="8" :sm="12" :xs="24">
                <el-form-item label="类别" class="compact-form-item">
                  <el-select v-model="state.tableQueryParams.category" placeholder="请选择类别" clearable
                    style="width: 100%" size="default">
                    <el-option label="姓名桌牌" value="1" />
                    <el-option label="价格标签" value="2" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xl="4" :lg="6" :md="8" :sm="12" :xs="24">
                <el-form-item label="结果" class="compact-form-item">
                  <el-select v-model="state.tableQueryParams.result" placeholder="请选择结果" clearable
                    style="width: 100%" size="default">
                    <el-option label="已连接" value="connected" />
                    <el-option label="连接中" value="connecting" />
                    <el-option label="未连接" value="disconnected" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xl="4" :lg="6" :md="8" :sm="12" :xs="24">
                <el-form-item class="compact-form-item button-form-item">
                  <div class="query-buttons">
                    <el-button icon="ele-Search" type="primary" @click="handleQuery" size="default"> 查询 </el-button>
                    <el-button icon="ele-Refresh" @click="() => state.tableQueryParams = {}" size="default"> 重置 </el-button>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 操作按钮行 -->
            <el-row class="action-row">
              <el-col :span="24">
                <div class="action-buttons">
                  <div class="action-buttons-left">
                    <el-button type="primary" plain size="default"> 当前等待任务 </el-button>
                    <el-button type="warning" plain @click="editDialogRef.openDialog(null, '新增蓝牙桌牌设备')" size="default"> 导入 </el-button>
                    <el-button type="success" plain size="default"> 导出 </el-button>
                  </div>
                  <div class="action-buttons-right">
                    <el-button type="danger" plain @click="batchDelDevices" :disabled="state.selectData.length == 0" size="default">
                      删除选中 ({{ state.selectData.length }})
                    </el-button>
                  </div>
                </div>
              </el-col>
            </el-row>
          </el-form>
        </el-card>

        <!-- 右下：设备列表 -->
        <el-card class="table-card" shadow="hover">
          <el-table :data="state.tableData" @selection-change="(val: any[]) => { state.selectData = val; }"
            style="width: 100%" v-loading="state.tableLoading" tooltip-effect="light" row-key="id"
            @sort-change="sortChange"
            :header-cell-style="{ background: '#f8f9fa', color: '#495057', fontWeight: 'bold', height: '60px' }"
            :row-style="{ height: '80px' }" :cell-style="{ padding: '16px 0' }" border>
            <el-table-column type="selection" width="40" align="center"
              v-if="auth('devices:batchDelete') || auth('devices:export')" />
            <el-table-column type="index" label="序号" width="55" align="center" />
            <el-table-column prop='serialNumber' label='编号' show-overflow-tooltip />
            <el-table-column prop='seat' label='座位' show-overflow-tooltip />
            <el-table-column prop='meetingRoom' label='会议室编号' show-overflow-tooltip />
            <el-table-column prop='workstation' label='工位号' show-overflow-tooltip />
            <el-table-column prop='category' label='类别' show-overflow-tooltip>
              <template #default="scope">
                <el-tag :type="scope.row.category === '1' ? 'primary' : 'success'">{{ scope.row.category === '1' ?
                  '姓名桌牌' : '价格标签' }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop='result' label='结果' show-overflow-tooltip />
            <el-table-column prop='signal' label='信号' show-overflow-tooltip>
              <template #default="scope">
                <el-tag
                  :type="scope.row.signal === 'strong' ? 'success' : scope.row.signal === 'medium' ? 'warning' : 'danger'">
                  {{ scope.row.signal === 'strong' ? '强' : scope.row.signal === 'medium' ? '中' : '弱' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop='gatewayMac' label='网关MAC' show-overflow-tooltip />
            <el-table-column prop='battery' label='电量' show-overflow-tooltip>
              <template #default="scope">
                <el-progress :percentage="scope.row.battery"
                  :color="scope.row.battery > 50 ? '#67c23a' : scope.row.battery > 20 ? '#e6a23c' : '#f56c6c'" />
              </template>
            </el-table-column>
            <el-table-column label="操作" width="140" align="center" fixed="right" show-overflow-tooltip
              v-if="auth('devices:update') || auth('devices:delete')">
              <template #default="scope">
                <el-button icon="ele-Edit" size="small" text type="primary"
                  @click="editDialogRef.openDialog(scope.row, '编辑')" v-auth="'devices:update'"> 编辑 </el-button>
                <el-button icon="ele-Delete" size="small" text type="primary" @click="delDevices(scope.row)"
                  v-auth="'devices:delete'"> 删除 </el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination v-model:currentPage="state.tableParams.page" v-model:page-size="state.tableParams.pageSize"
            @size-change="(val: any) => handleQuery({ pageSize: val })"
            @current-change="(val: any) => handleQuery({ page: val })" layout="total, sizes, prev, pager, next, jumper"
            :page-sizes="[10, 20, 50, 100, 200, 500]" :total="state.tableParams.total" size="small" background />
        </el-card>
      </div>
    </div>

    <printDialog ref="printDialogRef" :title="'打印蓝牙桌牌设备'" @reloadTable="handleQuery" />
    <editDialog ref="editDialogRef" @reloadTable="handleQuery" />
  </div>
</template>
<style scoped>
.devices-container {
  height: calc(100vh - 120px); /* 减去顶部导航和底部边距 */
  background-color: #f8f9fa;
  padding: 16px;
  box-sizing: border-box;
  overflow: hidden; /* 防止整体页面滚动 */
}

.layout-container {
  display: flex;
  height: 100%;
  gap: 16px;
}

.left-panel {
  width: 360px;
  flex-shrink: 0;
  height: 100%;
  overflow: hidden; /* 确保左侧面板不会溢出 */
}

.right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
  min-width: 0;
  height: 100%;
  overflow: hidden; /* 确保右侧面板不会溢出 */

  @media (max-width: 1200px) {
    gap: 12px;
  }
}

.room-card {
  height: 100%;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  background: #ffffff;
  border: 1px solid #e9ecef;
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 确保卡片内容不会溢出 */
}

.room-card :deep(.el-card__header) {
  background-color: #ffffff;
  color: #343a40;
  border-radius: 16px 16px 0 0;
  padding: 20px 24px;
  border-bottom: 1px solid #e9ecef;
  flex-shrink: 0;
}

.room-card :deep(.el-card__body) {
  padding: 0;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 确保卡片主体不会溢出 */
}

.card-header {
  font-weight: 700;
  font-size: 18px;
  color: white;
  display: flex;
  align-items: center;
  gap: 10px;
}

.card-header::before {
  content: '🏢';
  font-size: 20px;
}






.room-list {
  flex: 1;
  min-height: 0;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 20px 16px;
  box-sizing: border-box;
  /* 确保滚动条始终可见 */
  scrollbar-width: thin;
  scrollbar-color: #409eff #f5f7fa;
}

/* Webkit浏览器滚动条样式 */
.room-list::-webkit-scrollbar {
  width: 6px;
}

.room-list::-webkit-scrollbar-track {
  background: #f5f7fa;
  border-radius: 3px;
}

.room-list::-webkit-scrollbar-thumb {
  background: #409eff;
  border-radius: 3px;
  transition: background 0.3s ease;
}

.room-list::-webkit-scrollbar-thumb:hover {
  background: #337ecc;
}

/* 确保滚动条在内容溢出时显示 */
.room-list:hover::-webkit-scrollbar-thumb {
  background: #409eff;
}

.room-item {
  padding: 16px;
  margin-bottom: 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #e9ecef;
  background: #ffffff;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.room-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
  border-color: #dee2e6;
}

.room-item.active {
  background: #409eff;
  color: white;
  transform: translateY(-4px) scale(1.01);
  box-shadow: 0 16px 40px rgba(64, 158, 255, 0.3);
  border-color: #409eff;
}

.room-item.active::before {
  opacity: 1;
}

.room-item.active .room-name {
  color: white;
  font-weight: 700;
}

.room-item.active .device-count {
  color: white;
  background: rgba(255, 255, 255, 0.2);
}

.room-content {
  position: relative;
  z-index: 1;
}

.room-name {
  font-weight: 600;
  font-size: 16px;
  color: #2c3e50;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
  letter-spacing: 0.2px;
  line-height: 1.4;
  transition: all 0.3s ease;
}

.room-name::before {
  content: '📍';
  font-size: 14px;
}

.room-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 13px;
  margin-top: 6px;
}

.device-count {
  color: #7f8c8d;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
  background: rgba(127, 140, 141, 0.1);
  padding: 6px 12px;
  border-radius: 12px;
  font-size: 13px;
}

.device-count::before {
  content: '📱';
  font-size: 12px;
}

.status {
  padding: 6px 16px;
  border-radius: 20px;
  font-size: 13px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.2px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.status.online {
  background: linear-gradient(135deg, #00d4aa 0%, #00b894 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(0, 212, 170, 0.3);
  animation: pulse 2s infinite;
}

.status.online::before {
  content: '●';
}

.status.offline {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.button-group {
  display: flex;
  gap: 12px;
}

.query-card {
  flex-shrink: 0;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  background: #ffffff;
  border: 1px solid #e9ecef;
}

.query-card :deep(.el-card__body) {
  padding: 20px;
}

.query-card :deep(.el-form-item__label) {
  font-weight: normal;
  color: #606266;
}

.query-card :deep(.el-input__wrapper) {
  border-radius: 4px;
  box-shadow: none;
  border: 1px solid #dcdfe6;
}

.query-card :deep(.el-input__wrapper:hover) {
  border-color: #c0c4cc;
}

.query-card :deep(.el-input__wrapper.is-focus) {
  border-color: #409eff;
  box-shadow: none;
}

.query-card :deep(.el-button) {
  border-radius: 4px;
  font-weight: normal;
  transition: all 0.3s ease;
}

.query-card :deep(.el-button--primary) {
  background-color: #409eff;
  border-color: #409eff;
  box-shadow: none;
}

.query-card :deep(.el-button--primary:hover) {
  background-color: #66b1ff;
  border-color: #66b1ff;
  transform: none;
  box-shadow: none;
}

/* 查询表单样式优化 */
.query-form {
  margin: 0;
}

.query-row {
  margin-bottom: 16px;
}

.action-row {
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
  margin-top: 8px;
}

.compact-form-item {
  margin-bottom: 0 !important;
}

.compact-form-item :deep(.el-form-item__label) {
  font-size: 14px;
  font-weight: 500;
  color: #606266;
  line-height: 32px;
  padding-bottom: 0;
}

.compact-form-item :deep(.el-form-item__content) {
  line-height: 32px;
}

.button-form-item :deep(.el-form-item__label) {
  visibility: hidden; /* 隐藏按钮列的标签但保持布局 */
}

.query-buttons {
  display: flex;
  gap: 8px;
  justify-content: flex-start;
}

.action-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
}

.action-buttons-left {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.action-buttons-right {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

/* 响应式优化 */
@media (max-width: 1400px) {
  .query-row .el-col {
    margin-bottom: 12px;
  }

  .action-buttons {
    flex-direction: column;
    align-items: stretch;
  }

  .action-buttons-left,
  .action-buttons-right {
    justify-content: center;
  }
}

.table-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  background: #ffffff;
  border: 1px solid #e9ecef;
  min-height: 0; /* 确保flex子项可以收缩 */
  overflow: hidden; /* 防止内容溢出 */
}

.table-card :deep(.el-card__body) {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 24px;
  min-height: 0; /* 确保flex子项可以收缩 */
  overflow: hidden; /* 防止内容溢出 */
}

.table-card :deep(.el-table) {
  flex: 1;
  border-radius: 12px;
  overflow: auto; /* 允许表格内容滚动 */
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.04);
  min-height: 0; /* 确保表格可以收缩 */
}

.table-card :deep(.el-table__header) {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.table-card :deep(.el-table th) {
  background: transparent;
  font-weight: 700;
  color: #374151;
  border-bottom: 2px solid #e5e7eb;
}

.table-card :deep(.el-table td) {
  border-bottom: 1px solid #f3f4f6;
}

.table-card :deep(.el-table__row:hover) {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.table-card :deep(.el-tag) {
  border-radius: 20px;
  font-weight: 600;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-card :deep(.el-progress-bar__outer) {
  border-radius: 10px;
  background: #f3f4f6;
}

.table-card :deep(.el-progress-bar__inner) {
  border-radius: 10px;
}

.table-card :deep(.el-pagination) {
  margin-top: 20px;
  justify-content: center;
  flex-shrink: 0; /* 分页器不收缩 */
}

.table-card :deep(.el-pagination .btn-next),
.table-card :deep(.el-pagination .btn-prev) {
  border-radius: 8px;
}

.table-card :deep(.el-pagination .el-pager li) {
  border-radius: 8px;
  margin: 0 2px;
}

.table-card :deep(.el-pagination .el-pager li.is-active) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.mb10 {
  margin-bottom: 10px;
}

.mb20 {
  margin-bottom: 28px;
}



:deep(.el-form-item) {
  margin-bottom: 20px;
}

:deep(.el-form-item__label) {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  line-height: 1.5;
  padding-bottom: 8px;
}

:deep(.el-input__wrapper) {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 2px solid rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

:deep(.el-input__wrapper:hover) {
  border-color: rgba(102, 126, 234, 0.3);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: #667eea;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.2);
}

:deep(.el-select .el-input__wrapper) {
  border-radius: 12px;
}

:deep(.el-button) {
  border-radius: 12px;
  font-weight: 600;
  letter-spacing: 0.3px;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

:deep(.el-button--primary) {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-color: transparent;
}

:deep(.el-button--primary:hover) {
  background: linear-gradient(135deg, #5a6fd8, #6a4190);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

:deep(.el-button--primary.is-plain) {
  color: #409eff;
  background: #ecf5ff;
  border-color: #b3d8ff;
}

:deep(.el-button--primary.is-plain:hover) {
  background: #409eff;
  border-color: #409eff;
  color: #fff;
}

:deep(.el-button--danger.is-plain) {
  color: #f56c6c;
  background: #fef0f0;
  border-color: #fbc4c4;
}

:deep(.el-button--danger.is-plain:hover) {
  background: #f56c6c;
  border-color: #f56c6c;
  color: #fff;
}

:deep(.el-button--warning.is-plain) {
  color: #e6a23c;
  background: #fdf6ec;
  border-color: #f5dab1;
}

:deep(.el-button--warning.is-plain:hover) {
  background: #e6a23c;
  border-color: #e6a23c;
  color: #fff;
}

:deep(.el-button--success.is-plain) {
  color: #67c23a;
  background: #f0f9eb;
  border-color: #c2e7b0;
}

:deep(.el-button--success.is-plain:hover) {
  background: #67c23a;
  border-color: #67c23a;
  color: #fff;
}

/* 响应式设计 */


@media (max-width: 1400px) {
  .left-panel {
    width: 320px;
  }
}

@media (max-width: 1200px) {
  .devices-container {
    height: auto;
    min-height: calc(100vh - 120px);
  }

  .layout-container {
    flex-direction: column;
    height: auto;
    gap: 12px;
  }

  .left-panel {
    width: 100%;
    height: auto;
    max-height: 50vh;
  }

  .right-panel {
    width: 100%;
    height: auto;
  }

  .room-card {
    height: auto;
    max-height: 50vh;
  }

  .room-card :deep(.el-card__body) {
    height: auto;
    max-height: 40vh;
  }

  .table-card {
    height: auto;
    min-height: 60vh;
  }
}

@media (max-width: 768px) {
  .devices-container {
    padding: 16px;
  }

  .el-row {
    flex-direction: column;
    gap: 0;
  }

  .el-col {
    width: 100%;
    max-width: 100%;
    margin-bottom: 16px;
  }

  .query-card .el-form-item {
    margin-bottom: 0;
  }
}

@media (max-width: 480px) {
  .room-list {
    padding: 16px;
  }

  .room-item {
    padding: 16px;
  }

  .room-name {
    font-size: 16px;
  }
}

:deep(.el-input), :deep(.el-select), :deep(.el-input-number) {
  width: 100%;
}
</style>