<script lang="ts" setup name="devices">
import { ref, reactive, onMounted } from "vue";
import { auth } from '/@/utils/authFunction';
import { ElMessageBox, ElMessage } from "element-plus";
import { useDevicesApi } from '/@/api/device/devices';
import editDialog from '/@/views/device/devices/component/editDialog.vue'
import printDialog from '/@/views/system/print/component/hiprint/preview.vue'
import { Monitor, Connection, Warning, Close } from '@element-plus/icons-vue'

const devicesApi = useDevicesApi();
const printDialogRef = ref();
const editDialogRef = ref();

const state = reactive({
  exportLoading: false,
  tableLoading: false,
  roomLoading: false,
  selectData: [] as any[],
  selectedRoom: null as any,
  tableQueryParams: {} as any,
  tableParams: {
    page: 1,
    pageSize: 20,
    total: 3,
    field: 'createTime',
    order: 'descending',
    descStr: 'descending',
  },
  tableData: [
    {
      id: 1,
      serialNumber: '46C101012287',
      seat: '座位1',
      meetingRoom: '会议室01',
      workstation: '工位001',
      category: '1',
      result: '已连接',
      signal: 'strong',
      gatewayMac: '65:C4:D8:81:50:32',
      battery: 85
    },
    {
      id: 2,
      serialNumber: '46C101012288',
      seat: '座位2',
      meetingRoom: '会议室01',
      workstation: '工位002',
      category: '2',
      result: '已连接',
      signal: 'medium',
      gatewayMac: '65:C4:D8:81:50:33',
      battery: 65
    },
    {
      id: 3,
      serialNumber: '46C101012289',
      seat: '座位3',
      meetingRoom: '会议室02',
      workstation: '工位003',
      category: '1',
      result: '连接中',
      signal: 'weak',
      gatewayMac: '65:C4:D8:81:50:34',
      battery: 25
    }
  ],
  roomList: [
    { id: 1, name: '会议室01', deviceCount: 5, status: 'online' },
    { id: 2, name: '会议室02', deviceCount: 3, status: 'offline' },
    { id: 3, name: '会议室03', deviceCount: 8, status: 'online' },
    { id: 4, name: '会议室04', deviceCount: 2, status: 'online' },
    { id: 5, name: '会议室05', deviceCount: 6, status: 'offline' },
  ],
});

// 页面加载时
onMounted(async () => {
  state.selectedRoom = { id: null, name: '所有会议室' };
  handleQuery();
});

// 选择会议室
const selectRoom = (room: any) => {
  state.selectedRoom = room;
  if (room.id === null) {
    delete state.tableQueryParams.roomId;
  } else {
    state.tableQueryParams.roomId = room.id;
  }
  handleQuery();
};

// 查询操作
const handleQuery = async (params: any = {}) => {
  state.tableLoading = true;
  state.tableParams = Object.assign(state.tableParams, params);
  const queryParams = Object.assign({}, state.tableQueryParams, state.tableParams);
  if (state.selectedRoom) {
    queryParams.roomId = state.selectedRoom.id;
  }

  // 模拟API调用
  setTimeout(() => {
    state.tableLoading = false;
  }, 500);
};

// 重置查询条件
const resetQuery = () => {
  state.tableQueryParams = {};
  handleQuery();
};

// 列排序
const sortChange = async (column: any) => {
  state.tableParams.field = column.prop;
  state.tableParams.order = column.order;
  await handleQuery();
};

// 删除
const delDevices = (row: any) => {
  ElMessageBox.confirm(`确定要删除吗?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    ElMessage.success("删除成功");
    handleQuery();
  }).catch(() => {});
};

// 批量删除
const batchDelDevices = () => {
  ElMessageBox.confirm(`确定要删除${state.selectData.length}条记录吗?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    ElMessage.success(`成功批量删除${state.selectData.length}条记录`);
    handleQuery();
  }).catch(() => {});
};

// 导出数据
const exportData = () => {
  state.exportLoading = true;
  ElMessage.success("导出功能开发中...");
  setTimeout(() => {
    state.exportLoading = false;
  }, 2000);
};

// 刷新设备
const refreshDevices = () => {
  ElMessage.info("正在刷新设备状态...");
  handleQuery();
};

// 编辑设备
const editDevice = (row: any) => {
  editDialogRef.value.openDialog(row, '编辑蓝牙桌牌设备');
};

// 查看设备详情
const viewDevice = (row: any) => {
  ElMessage.info(`查看设备详情: ${row.serialNumber}`);
};

// 处理下拉菜单命令
const handleCommand = (command: string, row: any) => {
  switch (command) {
    case 'print':
      printDialogRef.value.openDialog(row);
      break;
    case 'reset':
      ElMessageBox.confirm(`确定要重置设备 ${row.serialNumber} 吗?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        ElMessage.success("设备重置成功");
      }).catch(() => {});
      break;
    case 'test':
      ElMessage.info(`正在测试设备 ${row.serialNumber} 连接...`);
      break;
    case 'delete':
      delDevices(row);
      break;
  }
};
</script>

<template>
  <div class="devices-container" v-loading="state.exportLoading">
    <div class="layout-container">
      <!-- 左侧会议室列表 -->
      <div class="left-panel">
        <el-card shadow="hover" class="room-card">
          <template #header>
            <div class="card-header">
              <span>会议室列表</span>
              <el-badge :value="state.roomList.length" class="room-count-badge" type="primary" />
            </div>
          </template>
          <div class="room-list" v-loading="state.roomLoading">
            <!-- 所有会议室选项 -->
            <div class="room-item all-rooms" :class="{ active: state.selectedRoom?.id === null }"
              @click="selectRoom({ id: null, name: '所有会议室' })">
              <div class="room-content">
                <div class="room-details">
                  <div class="room-name">所有会议室</div>
                  <div class="room-summary">查看全部设备</div>
                </div>
              </div>
            </div>
            <!-- 会议室列表 -->
            <div v-for="room in state.roomList" :key="room.id" class="room-item"
              :class="{ active: state.selectedRoom?.id === room.id }" @click="selectRoom(room)">
              <div class="room-content">
                <div class="room-details">
                  <div class="room-name">{{ room.name }}</div>
                  <div class="room-info">
                    <span class="device-count">设备: {{ room.deviceCount }}台</span>
                    <span class="status" :class="room.status">{{ room.status === 'online' ? '在线' : '离线' }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 右侧内容区域 -->
      <div class="right-panel">
        <!-- 统计信息面板 -->
        <div class="stats-panel">
          <el-row :gutter="16">
            <el-col :xl="6" :lg="6" :md="12" :sm="12" :xs="24">
              <el-card class="stats-card total-devices">
                <div class="stats-content">
                  <div class="stats-icon">
                    <el-icon size="24"><Monitor /></el-icon>
                  </div>
                  <div class="stats-info">
                    <div class="stats-number">{{ state.tableData.length }}</div>
                    <div class="stats-label">设备总数</div>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :xl="6" :lg="6" :md="12" :sm="12" :xs="24">
              <el-card class="stats-card online-devices">
                <div class="stats-content">
                  <div class="stats-icon">
                    <el-icon size="24"><Connection /></el-icon>
                  </div>
                  <div class="stats-info">
                    <div class="stats-number">{{ state.tableData.filter(d => d.result === '已连接').length }}</div>
                    <div class="stats-label">在线设备</div>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :xl="6" :lg="6" :md="12" :sm="12" :xs="24">
              <el-card class="stats-card low-battery">
                <div class="stats-content">
                  <div class="stats-icon">
                    <el-icon size="24"><Warning /></el-icon>
                  </div>
                  <div class="stats-info">
                    <div class="stats-number">{{ state.tableData.filter(d => d.battery < 20).length }}</div>
                    <div class="stats-label">低电量</div>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :xl="6" :lg="6" :md="12" :sm="12" :xs="24">
              <el-card class="stats-card weak-signal">
                <div class="stats-content">
                  <div class="stats-icon">
                    <el-icon size="24"><Close /></el-icon>
                  </div>
                  <div class="stats-info">
                    <div class="stats-number">{{ state.tableData.filter(d => d.signal === 'weak').length }}</div>
                    <div class="stats-label">信号弱</div>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>

        <!-- 查询条件区域 -->
        <el-card shadow="hover" class="query-card">
          <el-form :model="state.tableQueryParams" ref="queryForm" labelWidth="80px" class="query-form">
            <el-row :gutter="16" class="query-row">
              <el-col :xl="4" :lg="6" :md="8" :sm="12" :xs="24">
                <el-form-item label="设备编号" class="compact-form-item">
                  <el-input v-model="state.tableQueryParams.serialNumber" placeholder="请输入设备编号" clearable size="default" />
                </el-form-item>
              </el-col>
              <el-col :xl="4" :lg="6" :md="8" :sm="12" :xs="24">
                <el-form-item label="座位号" class="compact-form-item">
                  <el-input v-model="state.tableQueryParams.seat" placeholder="请输入座位号" clearable size="default" />
                </el-form-item>
              </el-col>
              <el-col :xl="4" :lg="6" :md="8" :sm="12" :xs="24">
                <el-form-item label="设备类别" class="compact-form-item">
                  <el-select v-model="state.tableQueryParams.category" placeholder="请选择类别" clearable size="default" style="width: 100%">
                    <el-option label="姓名桌牌" value="1" />
                    <el-option label="价格标签" value="2" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xl="4" :lg="6" :md="8" :sm="12" :xs="24">
                <el-form-item label="连接状态" class="compact-form-item">
                  <el-select v-model="state.tableQueryParams.result" placeholder="请选择状态" clearable size="default" style="width: 100%">
                    <el-option label="已连接" value="已连接" />
                    <el-option label="连接中" value="连接中" />
                    <el-option label="未连接" value="未连接" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xl="4" :lg="6" :md="8" :sm="12" :xs="24">
                <el-form-item label="信号强度" class="compact-form-item">
                  <el-select v-model="state.tableQueryParams.signal" placeholder="请选择信号" clearable size="default" style="width: 100%">
                    <el-option label="强" value="strong" />
                    <el-option label="中" value="medium" />
                    <el-option label="弱" value="weak" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xl="4" :lg="6" :md="8" :sm="12" :xs="24">
                <el-form-item class="compact-form-item button-form-item">
                  <div class="query-buttons">
                    <el-button icon="ele-Search" type="primary" @click="handleQuery" size="default"> 查询 </el-button>
                    <el-button icon="ele-Refresh" @click="resetQuery" size="default"> 重置 </el-button>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
            
            <!-- 操作按钮行 -->
            <el-row class="action-row">
              <el-col :span="24">
                <div class="action-buttons">
                  <div class="action-buttons-left">
                    <el-button type="primary" plain size="default" icon="ele-Clock">
                      当前等待任务
                      <el-badge :value="3" class="task-badge" />
                    </el-button>
                    <el-button type="warning" plain @click="editDialogRef.openDialog(null, '新增蓝牙桌牌设备')" size="default" icon="ele-Upload">
                      导入设备
                    </el-button>
                    <el-button type="success" plain @click="exportData" size="default" icon="ele-Download">
                      导出数据
                    </el-button>
                    <el-button type="info" plain @click="refreshDevices" size="default" icon="ele-Refresh">
                      刷新设备
                    </el-button>
                  </div>
                  <div class="action-buttons-right">
                    <el-button type="danger" plain @click="batchDelDevices" :disabled="state.selectData.length == 0" size="default" icon="ele-Delete">
                      删除选中 ({{ state.selectData.length }})
                    </el-button>
                  </div>
                </div>
              </el-col>
            </el-row>
          </el-form>
        </el-card>

        <!-- 设备列表表格 -->
        <el-card class="table-card" shadow="hover">
          <div class="table-wrapper">
            <el-table :data="state.tableData" @selection-change="(val: any[]) => { state.selectData = val; }"
              style="width: 100%" v-loading="state.tableLoading" tooltip-effect="light" row-key="id"
              @sort-change="sortChange"
              :header-cell-style="{ background: '#f8f9fa', color: '#495057', fontWeight: 'bold', height: '50px' }"
              :row-style="{ height: '60px' }" :cell-style="{ padding: '12px 0' }" border>
              <el-table-column type="selection" width="40" align="center" />
              <el-table-column type="index" label="序号" width="55" align="center" />
              <el-table-column prop='serialNumber' label='编号' show-overflow-tooltip />
              <el-table-column prop='seat' label='座位' show-overflow-tooltip />
              <el-table-column prop='meetingRoom' label='会议室编号' show-overflow-tooltip />
              <el-table-column prop='workstation' label='工位号' show-overflow-tooltip />
              <el-table-column prop='category' label='类别' show-overflow-tooltip width="120">
                <template #default="scope">
                  <el-tag :type="scope.row.category === '1' ? 'primary' : 'success'" effect="light">
                    {{ scope.row.category === '1' ? '姓名桌牌' : '价格标签' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop='result' label='连接状态' show-overflow-tooltip width="120">
                <template #default="scope">
                  <el-tag :type="scope.row.result === '已连接' ? 'success' : scope.row.result === '连接中' ? 'warning' : 'danger'" effect="light">
                    {{ scope.row.result }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop='signal' label='信号强度' show-overflow-tooltip width="120">
                <template #default="scope">
                  <el-tag :type="scope.row.signal === 'strong' ? 'success' : scope.row.signal === 'medium' ? 'warning' : 'danger'" effect="light">
                    {{ scope.row.signal === 'strong' ? '强' : scope.row.signal === 'medium' ? '中' : '弱' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop='gatewayMac' label='网关MAC' show-overflow-tooltip width="160">
                <template #default="scope">
                  <span style="font-family: 'Courier New', monospace; font-size: 12px;">{{ scope.row.gatewayMac }}</span>
                </template>
              </el-table-column>
              <el-table-column prop='battery' label='电量状态' show-overflow-tooltip width="140">
                <template #default="scope">
                  <div class="battery-display">
                    <el-progress :percentage="scope.row.battery" :stroke-width="8" :show-text="false"
                      :color="scope.row.battery > 50 ? '#67c23a' : scope.row.battery > 20 ? '#e6a23c' : '#f56c6c'" />
                    <span class="battery-text" :style="{ color: scope.row.battery > 20 ? '#606266' : '#f56c6c' }">
                      {{ scope.row.battery }}%
                    </span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="200" align="center" fixed="right" show-overflow-tooltip>
                <template #default="scope">
                  <div class="operation-buttons">
                    <el-button icon="ele-Edit" size="small" text type="primary" @click="editDevice(scope.row)"> 编辑 </el-button>
                    <el-button icon="ele-View" size="small" text type="success" @click="viewDevice(scope.row)"> 详情 </el-button>
                    <el-dropdown trigger="click" @command="(command: string) => handleCommand(command, scope.row)">
                      <el-button icon="ele-MoreFilled" size="small" text type="info" />
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item command="print" icon="ele-Printer">打印标签</el-dropdown-item>
                          <el-dropdown-item command="reset" icon="ele-RefreshRight">重置设备</el-dropdown-item>
                          <el-dropdown-item command="test" icon="ele-Connection">测试连接</el-dropdown-item>
                          <el-dropdown-item command="delete" icon="ele-Delete" divided>删除设备</el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="pagination-wrapper">
            <el-pagination v-model:currentPage="state.tableParams.page" v-model:page-size="state.tableParams.pageSize"
              @size-change="(val: any) => handleQuery({ pageSize: val })"
              @current-change="(val: any) => handleQuery({ page: val })" layout="total, sizes, prev, pager, next, jumper"
              :page-sizes="[10, 20, 50, 100, 200, 500]" :total="state.tableParams.total" size="small" background />
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 主容器样式 */
.devices-container {
  padding: 16px;
  background-color: #f8f9fa;
  height: calc(100vh - 120px);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.layout-container {
  display: flex;
  gap: 16px;
  flex: 1;
  min-height: 0;
  overflow: hidden;
}

/* 左侧面板样式 */
.left-panel {
  width: 300px;
  flex-shrink: 0;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.room-card {
  flex: 1;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  background: #ffffff;
  border: 1px solid #e9ecef;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.room-card :deep(.el-card__header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px 12px 0 0;
  padding: 16px 20px;
  border-bottom: none;
}

.card-header {
  font-weight: 600;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.room-count-badge {
  margin-left: auto;
}

.room-card :deep(.el-card__body) {
  padding: 0;
  flex: 1;
  display: flex;
  flex-direction: column;
  max-height: 500px;
  overflow: hidden;
}

.room-list {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.room-item {
  padding: 12px 16px;
  margin-bottom: 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #e9ecef;
  background: #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
}

.room-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border-color: #409eff;
}

.room-item.active {
  background: #409eff;
  color: white;
  border-color: #409eff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.room-item.all-rooms {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
  border-color: transparent;
}

.room-item.all-rooms:hover {
  background: linear-gradient(135deg, #f093fb 20%, #f5576c 80%);
}

.room-item.all-rooms.active {
  background: linear-gradient(135deg, #e91e63 0%, #f44336 100%);
}

.room-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.room-details {
  flex: 1;
  min-width: 0;
}

.room-name {
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 4px;
}

.room-summary {
  font-size: 12px;
  opacity: 0.8;
  color: #909399;
}

.room-item.all-rooms .room-summary {
  color: rgba(255, 255, 255, 0.8);
}

.room-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  gap: 8px;
}

.device-count {
  color: #7f8c8d;
  font-weight: 500;
  background: rgba(127, 140, 141, 0.1);
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 11px;
}

.room-item.active .device-count {
  color: white;
  background: rgba(255, 255, 255, 0.2);
}

.status {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
}

.status.online {
  background: #67c23a;
  color: white;
}

.status.offline {
  background: #f56c6c;
  color: white;
}

/* 右侧面板样式 */
.right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
  min-height: 0;
  height: 100%;
  overflow: hidden;
}

/* 统计面板样式 */
.stats-panel {
  flex-shrink: 0;
  margin-bottom: 0;
}

.stats-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.stats-card :deep(.el-card__body) {
  padding: 20px;
  height: 100px;
  display: flex;
  align-items: center;
}

.stats-content {
  display: flex;
  align-items: center;
  gap: 16px;
  width: 100%;
}

.stats-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 24px;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 14px;
  color: #909399;
  font-weight: 500;
}

/* 不同统计卡片的主题色 */
.total-devices {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.total-devices .stats-icon {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.total-devices .stats-label {
  color: rgba(255, 255, 255, 0.8);
}

.online-devices {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.online-devices .stats-icon {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.online-devices .stats-label {
  color: rgba(255, 255, 255, 0.8);
}

.low-battery {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  color: white;
}

.low-battery .stats-icon {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.low-battery .stats-label {
  color: rgba(255, 255, 255, 0.8);
}

.weak-signal {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  color: #333;
}

.weak-signal .stats-icon {
  background: rgba(255, 255, 255, 0.6);
  color: #666;
}

.weak-signal .stats-label {
  color: #666;
}

.query-card {
  flex-shrink: 0;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  background: #ffffff;
  border: 1px solid #e9ecef;
}

.query-card :deep(.el-card__body) {
  padding: 20px;
}

.query-form {
  margin: 0;
  display: flex;
  flex-direction: column;
}

.query-row {
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.query-row .el-col {
  margin-bottom: 12px;
}

.action-row {
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
  margin-top: 8px;
  flex-shrink: 0;
}

.compact-form-item {
  margin-bottom: 0 !important;
}

.compact-form-item :deep(.el-form-item__label) {
  font-size: 14px;
  font-weight: 500;
  color: #606266;
  line-height: 32px;
  padding-bottom: 0;
}

.button-form-item :deep(.el-form-item__label) {
  visibility: hidden;
}

.query-buttons {
  display: flex;
  gap: 8px;
}

.action-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
}

.action-buttons-left {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.action-buttons-right {
  display: flex;
  gap: 12px;
}

/* 任务徽章样式 */
.task-badge {
  margin-left: 8px;
}

.task-badge :deep(.el-badge__content) {
  background-color: #f56c6c;
  border: 1px solid #fff;
  font-size: 10px;
  height: 16px;
  line-height: 16px;
  min-width: 16px;
  padding: 0 4px;
}

/* 操作按钮组样式 */
.operation-buttons {
  display: flex;
  align-items: center;
  gap: 4px;
  justify-content: center;
}

.operation-buttons .el-button {
  margin: 0;
  padding: 4px 8px;
  font-size: 12px;
}

.operation-buttons .el-dropdown {
  margin-left: 4px;
}

/* 表格样式 */
.table-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  background: #ffffff;
  border: 1px solid #e9ecef;
  min-height: 0;
  overflow: hidden;
}

.table-card :deep(.el-card__body) {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 20px;
  min-height: 0;
  overflow: hidden;
}

.table-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow: hidden;
}

.table-wrapper :deep(.el-table) {
  flex: 1;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  display: flex;
  flex-direction: column;
}

.table-wrapper :deep(.el-table .el-table__header-wrapper) {
  flex-shrink: 0;
}

.table-wrapper :deep(.el-table .el-table__body-wrapper) {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
}

.pagination-wrapper {
  flex-shrink: 0;
  padding-top: 16px;
  display: flex;
  justify-content: center;
}

.battery-display {
  display: flex;
  align-items: center;
  gap: 8px;
}

.battery-text {
  font-size: 12px;
  font-weight: 600;
  min-width: 35px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .devices-container {
    height: calc(100vh - 100px);
  }

  .layout-container {
    flex-direction: column;
    gap: 12px;
  }

  .left-panel {
    width: 100%;
    height: auto;
    flex-shrink: 0;
  }

  .right-panel {
    flex: 1;
    min-height: 0;
  }

  .room-card {
    max-height: 300px;
  }
}

@media (max-width: 768px) {
  .devices-container {
    padding: 8px;
    height: calc(100vh - 80px);
  }

  .left-panel {
    max-height: 250px;
  }

  .action-buttons {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .action-buttons-left,
  .action-buttons-right {
    justify-content: center;
    flex-wrap: wrap;
  }

  .query-buttons {
    flex-direction: column;
    gap: 8px;
    width: 100%;
  }

  .query-buttons .el-button {
    width: 100%;
  }

  .operation-buttons {
    flex-direction: column;
    gap: 2px;
  }

  .operation-buttons .el-button {
    width: 100%;
    margin: 0;
  }
}
</style>
