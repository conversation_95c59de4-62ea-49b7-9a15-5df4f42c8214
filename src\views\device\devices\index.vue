<script lang="ts" setup name="devices">
import { ref, reactive, onMounted } from "vue";
import { auth } from '/@/utils/authFunction';
import { ElMessageBox, ElMessage } from "element-plus";
import { useDevicesApi } from '/@/api/device/devices';
import editDialog from '/@/views/device/devices/component/editDialog.vue'
import printDialog from '/@/views/system/print/component/hiprint/preview.vue'

const devicesApi = useDevicesApi();
const printDialogRef = ref();
const editDialogRef = ref();

const state = reactive({
  exportLoading: false,
  tableLoading: false,
  roomLoading: false,
  selectData: [] as any[],
  selectedRoom: null as any,
  tableQueryParams: {} as any,
  tableParams: {
    page: 1,
    pageSize: 20,
    total: 3,
    field: 'createTime',
    order: 'descending',
    descStr: 'descending',
  },
  tableData: [
    {
      id: 1,
      serialNumber: '46C101012287',
      seat: '座位1',
      meetingRoom: '会议室01',
      workstation: '工位001',
      category: '1',
      result: '已连接',
      signal: 'strong',
      gatewayMac: '65:C4:D8:81:50:32',
      battery: 85
    },
    {
      id: 2,
      serialNumber: '46C101012288',
      seat: '座位2',
      meetingRoom: '会议室01',
      workstation: '工位002',
      category: '2',
      result: '已连接',
      signal: 'medium',
      gatewayMac: '65:C4:D8:81:50:33',
      battery: 65
    },
    {
      id: 3,
      serialNumber: '46C101012289',
      seat: '座位3',
      meetingRoom: '会议室02',
      workstation: '工位003',
      category: '1',
      result: '连接中',
      signal: 'weak',
      gatewayMac: '65:C4:D8:81:50:34',
      battery: 25
    }
  ],
  roomList: [
    { id: 1, name: '会议室01', deviceCount: 5, status: 'online' },
    { id: 2, name: '会议室02', deviceCount: 3, status: 'offline' },
    { id: 3, name: '会议室03', deviceCount: 8, status: 'online' },
    { id: 4, name: '会议室04', deviceCount: 2, status: 'online' },
    { id: 5, name: '会议室05', deviceCount: 6, status: 'offline' },
    { id: 6, name: '会议室06', deviceCount: 4, status: 'online' },
    { id: 7, name: '会议室07', deviceCount: 7, status: 'offline' },
    { id: 8, name: '会议室08', deviceCount: 3, status: 'online' },
    { id: 9, name: '会议室09', deviceCount: 9, status: 'online' },
    { id: 10, name: '会议室10', deviceCount: 2, status: 'offline' },
    { id: 11, name: '会议室11', deviceCount: 5, status: 'online' },
    { id: 12, name: '会议室12', deviceCount: 6, status: 'online' },
    { id: 13, name: '会议室13', deviceCount: 4, status: 'offline' },
    { id: 14, name: '会议室14', deviceCount: 8, status: 'online' },
    { id: 15, name: '会议室15', deviceCount: 3, status: 'offline' },
  ],
});

// 页面加载时
onMounted(async () => {
  state.selectedRoom = { id: null, name: '所有会议室' };
  handleQuery();
});

// 选择会议室
const selectRoom = (room: any) => {
  state.selectedRoom = room;
  if (room.id === null) {
    delete state.tableQueryParams.roomId;
  } else {
    state.tableQueryParams.roomId = room.id;
  }
  handleQuery();
};

// 查询操作
const handleQuery = async (params: any = {}) => {
  state.tableLoading = true;
  state.tableParams = Object.assign(state.tableParams, params);
  const queryParams = Object.assign({}, state.tableQueryParams, state.tableParams);
  if (state.selectedRoom) {
    queryParams.roomId = state.selectedRoom.id;
  }
  const result = await devicesApi.page(queryParams).then(res => res.data.result);
  state.tableParams.total = result?.total;
  state.tableData = result?.items ?? [];
  state.tableLoading = false;
};

// 列排序
const sortChange = async (column: any) => {
  state.tableParams.field = column.prop;
  state.tableParams.order = column.order;
  await handleQuery();
};

// 删除
const delDevices = (row: any) => {
  ElMessageBox.confirm(`确定要删除吗?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    await devicesApi.delete({ id: row.id });
    handleQuery();
    ElMessage.success("删除成功");
  }).catch(() => {});
};

// 批量删除
const batchDelDevices = () => {
  ElMessageBox.confirm(`确定要删除${state.selectData.length}条记录吗?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    await devicesApi.batchDelete(state.selectData.map(u => ({ id: u.id }) )).then(res => {
      ElMessage.success(`成功批量删除${res.data.result}条记录`);
      handleQuery();
    });
  }).catch(() => {});
};

handleQuery();
</script>

<template>
  <div class="devices-container" v-loading="state.exportLoading">
    <div class="layout-container">
      <!-- 左侧会议室列表 -->
      <div class="left-panel">
        <el-card shadow="hover" class="room-card">
          <template #header>
            <div class="card-header">
              <el-icon class="header-icon"><OfficeBuilding /></el-icon>
              <span>会议室列表</span>
              <el-badge :value="state.roomList.length" class="room-count-badge" type="primary" />
            </div>
          </template>
          <div class="room-list" v-loading="state.roomLoading">
            <!-- 所有会议室选项 -->
            <div class="room-item all-rooms" :class="{ active: state.selectedRoom?.id === null }"
              @click="selectRoom({ id: null, name: '所有会议室' })">
              <div class="room-content">
                <div class="room-icon">
                  <el-icon><Grid /></el-icon>
                </div>
                <div class="room-details">
                  <div class="room-name">所有会议室</div>
                  <div class="room-summary">查看全部设备</div>
                </div>
              </div>
            </div>
            <!-- 会议室列表 -->
            <div v-for="room in state.roomList" :key="room.id" class="room-item"
              :class="{ active: state.selectedRoom?.id === room.id }" @click="selectRoom(room)">
              <div class="room-content">
                <div class="room-icon">
                  <el-icon><House /></el-icon>
                </div>
                <div class="room-details">
                  <div class="room-name">{{ room.name }}</div>
                  <div class="room-info">
                    <span class="device-count">
                      <el-icon><Monitor /></el-icon>
                      {{ room.deviceCount }}台设备
                    </span>
                    <span class="status" :class="room.status">
                      <el-icon><CircleDot /></el-icon>
                      {{ room.status === 'online' ? '在线' : '离线' }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 右侧内容区域 -->
      <div class="right-panel">
        <!-- 查询条件区域 -->
        <el-card shadow="hover" class="query-card">
          <el-form :model="state.tableQueryParams" ref="queryForm" labelWidth="80px" class="query-form">
            <el-row :gutter="16" class="query-row">
              <el-col :xl="4" :lg="6" :md="8" :sm="12" :xs="24">
                <el-form-item label="编号" class="compact-form-item">
                  <el-input v-model="state.tableQueryParams.serialNumber" placeholder="请输入编号" clearable size="default" />
                </el-form-item>
              </el-col>
              <el-col :xl="4" :lg="6" :md="8" :sm="12" :xs="24">
                <el-form-item label="座位" class="compact-form-item">
                  <el-input v-model="state.tableQueryParams.seat" placeholder="请输入座位" clearable size="default" />
                </el-form-item>
              </el-col>
              <el-col :xl="4" :lg="6" :md="8" :sm="12" :xs="24">
                <el-form-item label="工位号" class="compact-form-item">
                  <el-input v-model="state.tableQueryParams.workstation" placeholder="请输入工位号" clearable size="default" />
                </el-form-item>
              </el-col>
              <el-col :xl="4" :lg="6" :md="8" :sm="12" :xs="24">
                <el-form-item label="类别" class="compact-form-item">
                  <el-select v-model="state.tableQueryParams.category" placeholder="请选择类别" clearable
                    style="width: 100%" size="default">
                    <el-option label="姓名桌牌" value="1" />
                    <el-option label="价格标签" value="2" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xl="4" :lg="6" :md="8" :sm="12" :xs="24">
                <el-form-item label="结果" class="compact-form-item">
                  <el-select v-model="state.tableQueryParams.result" placeholder="请选择结果" clearable 
                    style="width: 100%" size="default">
                    <el-option label="已连接" value="connected" />
                    <el-option label="连接中" value="connecting" />
                    <el-option label="未连接" value="disconnected" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xl="4" :lg="6" :md="8" :sm="12" :xs="24">
                <el-form-item class="compact-form-item button-form-item">
                  <div class="query-buttons">
                    <el-button icon="ele-Search" type="primary" @click="handleQuery" size="default"> 查询 </el-button>
                    <el-button icon="ele-Refresh" @click="() => state.tableQueryParams = {}" size="default"> 重置 </el-button>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
            
            <!-- 操作按钮行 -->
            <el-row class="action-row">
              <el-col :span="24">
                <div class="action-buttons">
                  <div class="action-buttons-left">
                    <el-button type="primary" plain size="default">
                      <el-icon><Clock /></el-icon>
                      当前等待任务
                    </el-button>
                    <el-button type="warning" plain @click="editDialogRef.openDialog(null, '新增蓝牙桌牌设备')" size="default">
                      <el-icon><Upload /></el-icon>
                      导入设备
                    </el-button>
                    <el-button type="success" plain size="default">
                      <el-icon><Download /></el-icon>
                      导出数据
                    </el-button>
                  </div>
                  <div class="action-buttons-right">
                    <el-button type="danger" plain @click="batchDelDevices" :disabled="state.selectData.length == 0" size="default">
                      <el-icon><Delete /></el-icon>
                      删除选中 ({{ state.selectData.length }})
                    </el-button>
                  </div>
                </div>
              </el-col>
            </el-row>
          </el-form>
        </el-card>

        <!-- 设备列表表格 -->
        <el-card class="table-card" shadow="hover">
          <div class="table-wrapper">
            <el-table :data="state.tableData" @selection-change="(val: any[]) => { state.selectData = val; }"
              style="width: 100%" v-loading="state.tableLoading" tooltip-effect="light" row-key="id"
              @sort-change="sortChange"
              :header-cell-style="{ background: '#f8f9fa', color: '#495057', fontWeight: 'bold', height: '50px' }"
              :row-style="{ height: '60px' }" :cell-style="{ padding: '12px 0' }" border>
              <el-table-column type="selection" width="40" align="center"
                v-if="auth('devices:batchDelete') || auth('devices:export')" />
              <el-table-column type="index" label="序号" width="55" align="center" />
              <el-table-column prop='serialNumber' label='编号' show-overflow-tooltip />
              <el-table-column prop='seat' label='座位' show-overflow-tooltip />
              <el-table-column prop='meetingRoom' label='会议室编号' show-overflow-tooltip />
              <el-table-column prop='workstation' label='工位号' show-overflow-tooltip />
              <el-table-column prop='category' label='类别' show-overflow-tooltip width="120">
                <template #default="scope">
                  <el-tag :type="scope.row.category === '1' ? 'primary' : 'success'" effect="light">
                    <el-icon style="margin-right: 4px;">
                      <component :is="scope.row.category === '1' ? 'User' : 'PriceTag'" />
                    </el-icon>
                    {{ scope.row.category === '1' ? '姓名桌牌' : '价格标签' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop='result' label='连接状态' show-overflow-tooltip width="120">
                <template #default="scope">
                  <el-tag :type="scope.row.result === '已连接' ? 'success' : scope.row.result === '连接中' ? 'warning' : 'danger'" effect="light">
                    <el-icon style="margin-right: 4px;">
                      <component :is="scope.row.result === '已连接' ? 'CircleCheck' : scope.row.result === '连接中' ? 'Loading' : 'CircleClose'" />
                    </el-icon>
                    {{ scope.row.result }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop='signal' label='信号强度' show-overflow-tooltip width="120">
                <template #default="scope">
                  <el-tag :type="scope.row.signal === 'strong' ? 'success' : scope.row.signal === 'medium' ? 'warning' : 'danger'" effect="light">
                    <el-icon style="margin-right: 4px;">
                      <component :is="scope.row.signal === 'strong' ? 'Wifi' : scope.row.signal === 'medium' ? 'Connection' : 'Close'" />
                    </el-icon>
                    {{ scope.row.signal === 'strong' ? '强' : scope.row.signal === 'medium' ? '中' : '弱' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop='gatewayMac' label='网关MAC' show-overflow-tooltip width="160">
                <template #default="scope">
                  <div class="mac-address">
                    <el-icon style="margin-right: 4px; color: #909399;"><Connection /></el-icon>
                    <span style="font-family: 'Courier New', monospace; font-size: 12px;">{{ scope.row.gatewayMac }}</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop='battery' label='电量状态' show-overflow-tooltip width="140">
                <template #default="scope">
                  <div class="battery-display">
                    <el-progress :percentage="scope.row.battery" :stroke-width="8" :show-text="false"
                      :color="scope.row.battery > 50 ? '#67c23a' : scope.row.battery > 20 ? '#e6a23c' : '#f56c6c'" />
                    <span class="battery-text" :style="{ color: scope.row.battery > 20 ? '#606266' : '#f56c6c' }">
                      {{ scope.row.battery }}%
                    </span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="140" align="center" fixed="right" show-overflow-tooltip
                v-if="auth('devices:update') || auth('devices:delete')">
                <template #default="scope">
                  <el-button icon="ele-Edit" size="small" text type="primary"
                    @click="editDialogRef.openDialog(scope.row, '编辑')" v-auth="'devices:update'"> 编辑 </el-button>
                  <el-button icon="ele-Delete" size="small" text type="primary" @click="delDevices(scope.row)"
                    v-auth="'devices:delete'"> 删除 </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="pagination-wrapper">
            <el-pagination v-model:currentPage="state.tableParams.page" v-model:page-size="state.tableParams.pageSize"
              @size-change="(val: any) => handleQuery({ pageSize: val })"
              @current-change="(val: any) => handleQuery({ page: val })" layout="total, sizes, prev, pager, next, jumper"
              :page-sizes="[10, 20, 50, 100, 200, 500]" :total="state.tableParams.total" size="small" background />
          </div>
        </el-card>
      </div>
    </div>

    <printDialog ref="printDialogRef" :title="'打印蓝牙桌牌设备'" @reloadTable="handleQuery" />
    <editDialog ref="editDialogRef" @reloadTable="handleQuery" />
  </div>
</template>

<style scoped>
/* 主容器样式 */
.devices-container {
  padding: 16px;
  background-color: #f8f9fa;
  height: calc(100vh - 120px); /* 固定高度，减去顶部导航和padding */
  overflow: hidden; /* 防止整体页面滚动 */
  display: flex;
  flex-direction: column;
}

.layout-container {
  display: flex;
  gap: 16px;
  flex: 1; /* 占满剩余空间 */
  min-height: 0; /* 重要：允许flex子项收缩 */
  overflow: hidden;
}

/* 左侧面板样式 */
.left-panel {
  width: 300px;
  flex-shrink: 0;
  height: 100%; /* 占满父容器高度 */
  display: flex;
  flex-direction: column;
}

.room-card {
  flex: 1; /* 占满左侧面板高度 */
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  background: #ffffff;
  border: 1px solid #e9ecef;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 允许收缩 */
}

.room-card :deep(.el-card__header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px 12px 0 0;
  padding: 16px 20px;
  border-bottom: none;
}

.header-icon {
  font-size: 18px;
  margin-right: 8px;
}

.room-count-badge {
  margin-left: auto;
}

.room-card :deep(.el-card__body) {
  padding: 0;
  flex: 1;
  display: flex;
  flex-direction: column;
  max-height: 500px;
  overflow: hidden;
}

.card-header {
  font-weight: 600;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.room-list {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  scrollbar-width: thin;
  scrollbar-color: #409eff #f5f7fa;
}

.room-list::-webkit-scrollbar {
  width: 6px;
}

.room-list::-webkit-scrollbar-track {
  background: #f5f7fa;
  border-radius: 3px;
}

.room-list::-webkit-scrollbar-thumb {
  background: #409eff;
  border-radius: 3px;
}

.room-item {
  padding: 12px 16px;
  margin-bottom: 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #e9ecef;
  background: #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
}

.room-item.all-rooms {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
  border-color: transparent;
}

.room-item.all-rooms:hover {
  background: linear-gradient(135deg, #f093fb 20%, #f5576c 80%);
}

.room-item.all-rooms.active {
  background: linear-gradient(135deg, #e91e63 0%, #f44336 100%);
}

.room-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.room-icon {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(64, 158, 255, 0.1);
  color: #409eff;
  font-size: 16px;
  flex-shrink: 0;
}

.room-item.active .room-icon {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.room-item.all-rooms .room-icon {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.room-details {
  flex: 1;
  min-width: 0;
}

.room-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border-color: #409eff;
}

.room-item.active {
  background: #409eff;
  color: white;
  border-color: #409eff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.room-name {
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 4px;
  transition: all 0.3s ease;
}

.room-summary {
  font-size: 12px;
  opacity: 0.8;
  color: #909399;
}

.room-item.all-rooms .room-summary {
  color: rgba(255, 255, 255, 0.8);
}

.room-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  gap: 8px;
}

.device-count {
  color: #7f8c8d;
  font-weight: 500;
  background: rgba(127, 140, 141, 0.1);
  padding: 4px 8px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
}

.room-item.active .device-count {
  color: white;
  background: rgba(255, 255, 255, 0.2);
}

.status {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 4px;
}

.status.online {
  background: #67c23a;
  color: white;
}

.status.offline {
  background: #f56c6c;
  color: white;
}

/* 表格内容样式 */
.mac-address {
  display: flex;
  align-items: center;
}

.battery-display {
  display: flex;
  align-items: center;
  gap: 8px;
}

.battery-text {
  font-size: 12px;
  font-weight: 600;
  min-width: 35px;
}

/* 右侧面板样式 */
.right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
  min-height: 0; /* 允许收缩 */
  height: 100%; /* 占满父容器高度 */
  overflow: hidden; /* 防止溢出 */
}

.query-card {
  flex-shrink: 0;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  background: #ffffff;
  border: 1px solid #e9ecef;
}

.query-card :deep(.el-card__body) {
  padding: 20px;
}

.query-form {
  margin: 0;
  display: flex;
  flex-direction: column;
}

.query-row {
  margin-bottom: 16px;
  flex-wrap: wrap; /* 确保换行 */
}

.query-row .el-col {
  margin-bottom: 12px; /* 给每个表单项底部间距 */
}

.action-row {
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
  margin-top: 8px;
  flex-shrink: 0; /* 防止被压缩 */
}

.compact-form-item {
  margin-bottom: 0 !important;
}

.compact-form-item :deep(.el-form-item__label) {
  font-size: 14px;
  font-weight: 500;
  color: #606266;
  line-height: 32px;
  padding-bottom: 0;
}

.button-form-item :deep(.el-form-item__label) {
  visibility: hidden;
}

.query-buttons {
  display: flex;
  gap: 8px;
}

.action-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
}

.action-buttons-left {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.action-buttons-right {
  display: flex;
  gap: 12px;
}

/* 表格样式 */
.table-card {
  flex: 1; /* 占满剩余空间 */
  display: flex;
  flex-direction: column;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  background: #ffffff;
  border: 1px solid #e9ecef;
  min-height: 0; /* 允许收缩 */
  overflow: hidden; /* 防止溢出 */
}

.table-card :deep(.el-card__body) {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 20px;
  min-height: 0; /* 允许收缩 */
  overflow: hidden;
}

.table-wrapper {
  flex: 1; /* 占满剩余空间 */
  display: flex;
  flex-direction: column;
  min-height: 0; /* 允许收缩 */
  overflow: hidden;
}

.table-wrapper :deep(.el-table) {
  flex: 1;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  display: flex;
  flex-direction: column;
}

.table-wrapper :deep(.el-table .el-table__header-wrapper) {
  flex-shrink: 0; /* 表头不收缩 */
}

.table-wrapper :deep(.el-table .el-table__body-wrapper) {
  flex: 1; /* 表格内容占满剩余空间 */
  overflow-y: auto;
  overflow-x: hidden;
}

.pagination-wrapper {
  flex-shrink: 0;
  padding-top: 16px;
  display: flex;
  justify-content: center;
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(64, 158, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0);
  }
}

.room-item {
  animation: fadeInUp 0.3s ease-out;
}

.room-item.active {
  animation: pulse 2s infinite;
}

/* 表格行悬停效果 */
.table-wrapper :deep(.el-table__row:hover) {
  background-color: #f8f9fa !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

/* 按钮悬停效果增强 */
.action-buttons-left .el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.action-buttons-right .el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(245, 108, 108, 0.3);
}

/* 卡片悬停效果 */
.query-card:hover,
.table-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .devices-container {
    height: calc(100vh - 100px); /* 调整移动端高度 */
  }

  .layout-container {
    flex-direction: column;
    gap: 12px;
  }

  .left-panel {
    width: 100%;
    height: auto; /* 移动端不固定高度 */
    flex-shrink: 0;
  }

  .right-panel {
    flex: 1;
    min-height: 0;
  }

  .room-card {
    max-height: 300px; /* 限制会议室列表高度 */
  }



  .card-header {
    font-size: 14px;
  }

  .room-name {
    font-size: 13px;
  }
}

@media (max-width: 768px) {
  .devices-container {
    padding: 8px;
    height: calc(100vh - 80px); /* 小屏幕调整高度 */
  }

  .left-panel {
    max-height: 250px;
  }

  .action-buttons {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .action-buttons-left,
  .action-buttons-right {
    justify-content: center;
    flex-wrap: wrap;
  }

  .room-content {
    gap: 8px;
  }

  .room-icon {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }

  /* 确保查询按钮不被遮挡 */
  .query-row .el-col {
    margin-bottom: 12px;
  }

  .button-form-item {
    margin-top: 8px;
  }

  .query-buttons {
    flex-direction: column;
    gap: 8px;
    width: 100%;
  }

  .query-buttons .el-button {
    width: 100%;
  }
}

/* 加载状态优化 */
.el-loading-mask {
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(4px);
}

/* 滚动条美化 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}
</style>
