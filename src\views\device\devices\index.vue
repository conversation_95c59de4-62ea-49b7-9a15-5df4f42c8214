<script lang="ts" setup name="devices">
import { ref, reactive, onMounted, onUnmounted } from "vue";
import { auth } from '/@/utils/authFunction';
import { ElMessageBox, ElMessage } from "element-plus";
import { useDevicesApi } from '/@/api/device/devices';
import { useMeetingRoomsApi } from '/@/api/meeting/meetingRooms';
import BindDeviceDialog from './component/BindDeviceDialog.vue'
import EditDeviceDialog from './component/EditDeviceDialog.vue'
import editDialog from '/@/views/device/devices/component/editDialog.vue'
import printDialog from '/@/views/system/print/component/hiprint/preview.vue'
import { Monitor, Connection, Warning, Close, Link, Edit, Check } from '@element-plus/icons-vue'
import * as signalR from '@microsoft/signalr'

const devicesApi = useDevicesApi();
const meetingRoomsApi = useMeetingRoomsApi();
const printDialogRef = ref();
const editDialogRef = ref();

// SignalR连接
let connection: signalR.HubConnection | null = null;

const state = reactive({
  exportLoading: false,
  tableLoading: false,
  roomLoading: false,
  selectData: [] as any[],
  selectedRoom: null as any,
  tableQueryParams: {} as any,
  tableParams: {
    page: 1,
    pageSize: 20,
    total: 3,
    field: 'createTime',
    order: 'descending',
    descStr: 'descending',
  },
  // 统计数据
  statistics: {
    totalDevices: 0,
    onlineDevices: 0,
    lowBatteryDevices: 0,
    weakSignalDevices: 0
  },
  // WebSocket连接状态
  websocketConnected: false,
  connectionStatus: '未连接' as string,
  tableData: [],
  roomList: [] as any[],
});

// 获取设备统计数据
const getDeviceStatistics = async () => {
  try {
    const queryParams = {
      ...state.tableQueryParams,
      // 若会议室ID为null则传0查询所有会议室设备
      MeetingRoomId: state.selectedRoom?.id || 0
    };
    console.log('统计数据查询参数:', queryParams);
    const res = await devicesApi.statisticsByMeetingRoom(queryParams);
    console.log('统计数据API响应:', res.data);
    
    if (res.data && res.data.code === 200) {
      const newStatistics = {
        totalDevices: res.data.result.totalDevices || 0,
        onlineDevices: res.data.result.onlineDevices || 0,
        lowBatteryDevices: res.data.result.lowBatteryDevices || 0,
        weakSignalDevices: res.data.result.weakSignalDevices || 0
      };
      console.log('更新前统计数据:', state.statistics);
      console.log('更新后统计数据:', newStatistics);
      state.statistics = newStatistics;
    } else {
      console.warn('统计数据API返回异常:', res.data);
      // 如果API返回失败，尝试从当前表格数据计算统计
      calculateStatisticsFromTableData();
    }
  } catch (error) {
    console.error('获取设备统计数据失败:', error);
    // 如果API调用失败，尝试从当前表格数据计算统计
    calculateStatisticsFromTableData();
  }
};

// 从表格数据计算统计信息（备用方案）
const calculateStatisticsFromTableData = () => {
  if (!state.tableData || state.tableData.length === 0) {
    state.statistics = {
      totalDevices: 0,
      onlineDevices: 0,
      lowBatteryDevices: 0,
      weakSignalDevices: 0
    };
    return;
  }
  
  const total = state.tableData.length;
  const online = state.tableData.filter(device => device.status === 1 || device.status === '1').length;
  const lowBattery = state.tableData.filter(device => {
    const battery = parseInt(device.battery_level);
    return !isNaN(battery) && battery < 20;
  }).length;
  const weakSignal = state.tableData.filter(device => {
    const signal = parseInt(device.signal_strength);
    return !isNaN(signal) && signal < -70;
  }).length;
  
  const calculatedStats = {
    totalDevices: total,
    onlineDevices: online,
    lowBatteryDevices: lowBattery,
    weakSignalDevices: weakSignal
  };
  
  console.log('从表格数据计算的统计信息:', calculatedStats);
  state.statistics = calculatedStats;
};

// 获取会议室列表
const getRoomList = async () => {
  try {
    state.roomLoading = true;
    const res = await meetingRoomsApi.page({
      page: 1,
      pageSize: 1000 // 获取所有会议室
    }); 
    if (res.data.code === 200 && res.data.result.items) {
      console.log('会议室列表数据:', res.data.result.items);
      state.roomList = res.data.result.items.map((room: any) => ({
        id: room.id,
        name: room.room_name,
        deviceCount: 0, // 初始化为0，后续动态更新
        status: room.meeting_status === 1 ? 'online' : 'offline'
      }));
      
      // 获取会议室列表后，更新每个会议室的设备数量
      await updateRoomDeviceCounts();
    }
  } catch (error) {
    console.error('获取会议室列表失败:', error);
    ElMessage.error('获取会议室列表失败');
  } finally {
    state.roomLoading = false;
  }
};

// 更新会议室设备数量
const updateRoomDeviceCounts = async () => {
  try {
    // 获取所有设备数据来统计每个会议室的设备数量
    const allDevicesRes = await devicesApi.page({
      page: 1,
      pageSize: 10000 // 获取所有设备
    });
    
    if (allDevicesRes.data && allDevicesRes.data.code === 200 && allDevicesRes.data.result.items) {
      const allDevices = allDevicesRes.data.result.items;
      console.log('所有设备数据:', allDevices);
      
      // 统计每个会议室的设备数量
      const roomDeviceCount = {};
      allDevices.forEach(device => {
        const roomId = device.meeting_room_id || device.MeetingRoomId;
        if (roomId) {
          roomDeviceCount[roomId] = (roomDeviceCount[roomId] || 0) + 1;
        }
      });
      
      console.log('会议室设备数量统计:', roomDeviceCount);
      
      // 更新会议室列表中的设备数量
      state.roomList.forEach(room => {
        room.deviceCount = roomDeviceCount[room.id] || 0;
      });
      
      console.log('更新后的会议室列表:', state.roomList);
    }
  } catch (error) {
    console.error('更新会议室设备数量失败:', error);
  }
};

// 建立WebSocket连接
const establishWebSocketConnection = async () => {
  try {
    // 创建SignalR连接
    connection = new signalR.HubConnectionBuilder()
      .withUrl("/hubs/apStatus")
      .withAutomaticReconnect()
      .build();

    // 监听设备状态更新
    connection.on("DeviceStatusUpdated", (statusUpdates: any[]) => {
      console.log("收到设备状态更新：", statusUpdates);
      updateDeviceStatus(statusUpdates);
    });

    // 监听AP状态更新
    connection.on("APStatusUpdated", (statusUpdates: any[]) => {
      console.log("收到AP状态更新：", statusUpdates);
      // 可以根据需要处理AP状态更新
    });

    // 监听连接状态
    connection.on("JoinedGroup", (groupName: string) => {
      console.log(`成功加入组: ${groupName}`);
      state.connectionStatus = '已连接';
    });

    connection.on("Error", (message: string) => {
      console.error(`WebSocket错误: ${message}`);
      ElMessage.error(`WebSocket错误: ${message}`);
    });

    // 启动连接
    await connection.start();
    console.log("SignalR连接成功");
    state.websocketConnected = true;
    state.connectionStatus = '连接中';
    
    // 加入AP状态监听组
    await connection.invoke("JoinAPStatusGroup", "APStatus");
    
    // 请求当前状态
    await connection.invoke("RequestCurrentAPStatus");
    
  } catch (err) {
    console.error("SignalR连接失败:", err);
    state.websocketConnected = false;
    state.connectionStatus = '连接失败';
    ElMessage.error("WebSocket连接失败");
  }
};

// 更新设备状态
const updateDeviceStatus = (statusUpdates: any[]) => {
  statusUpdates.forEach(update => {
    console.log('收到设备状态更新:', update); 
    // 统一使用MAC地址匹配设备
    const updateMac = update.macAddress; 
    if (!updateMac) {
      console.warn('设备状态更新缺少MAC地址:', update);
      return;
    }
    
    const deviceIndex = state.tableData.findIndex(device => {
      return device.mac_address && device.mac_address === updateMac;
    });
    
    if (deviceIndex !== -1) {
      console.log('找到匹配设备，更新状态');
      // 更新设备状态
      const device = state.tableData[deviceIndex];
      
      // 更新连接状态
      const status = update.status;
      if (status !== undefined && status !== null) {
        device.status = status;
      }
      
      // 更新电量
      const batteryLevel = update.batteryLevel;
      if (batteryLevel !== undefined && batteryLevel !== null) {
        device.battery_level = batteryLevel;
      }
      
      // 更新信号强度
      const signalStrength = update.signalStrength;
      if (signalStrength !== undefined && signalStrength !== null) {
        device.signal_strength = signalStrength;
      }
      
      console.log(`设备 ${device.device_name} (MAC: ${device.mac_address}) 状态已更新:`, {
        连接状态: device.status,
        电量: device.battery_level + '%',
        信号强度: device.signal_strength + 'dBm',
        原始数据: update
      });
    } else {
      console.warn(`未找到MAC地址为 ${updateMac} 的设备:`, {
        更新数据: update,
        当前设备MAC列表: state.tableData.map(d => d.mac_address).filter(Boolean)
      });
    }
  });
  
  // 设备状态更新后，重新计算统计数据
  console.log('设备状态更新完成，重新计算统计数据');
  // 优先从表格数据计算，因为表格数据已经是最新的
  calculateStatisticsFromTableData();
  // 同时调用API获取最新统计（异步）
  getDeviceStatistics();
};

// 断开WebSocket连接
const disconnectWebSocket = async () => {
  if (connection) {
    try {
      await connection.stop();
      console.log("SignalR连接已断开");
    } catch (err) {
      console.error("断开SignalR连接失败:", err);
    } finally {
      connection = null;
      state.websocketConnected = false;
      state.connectionStatus = '未连接';
    }
  }
};

// 页面加载时
onMounted(async () => {
  state.selectedRoom = { id: null, name: '所有会议室' };
  await getRoomList();
  await getDeviceStatistics();
  handleQuery();
  // 建立WebSocket连接
  await establishWebSocketConnection();
});

// 页面卸载时
onUnmounted(async () => {
  await disconnectWebSocket();
});

// 选择会议室
const selectRoom = (room: any) => {
  console.log('选择会议室:', room);
  state.selectedRoom = room;
  
  // 清理之前的查询参数
  delete state.tableQueryParams.roomId;
  delete state.tableQueryParams.MeetingRoomId;
  
  // 如果选择的不是"所有会议室"，则添加会议室ID到查询参数
  if (room.id !== null && room.id !== undefined) {
    state.tableQueryParams.MeetingRoomId = room.id;
    console.log('设置会议室查询参数:', { MeetingRoomId: room.id });
  } else {
    console.log('选择所有会议室，不设置会议室查询参数');
  }
  
  // 重置分页参数
  state.tableParams.page = 1;
  
  // handleQuery中已经包含了getDeviceStatistics调用，无需重复调用
  handleQuery();
};

// 查询操作
const handleQuery = async (params: any = {}) => {
  try {
    state.tableLoading = true;
    state.tableParams = Object.assign(state.tableParams, params);
    const queryParams = Object.assign({}, state.tableQueryParams, state.tableParams);
    
    console.log('查询参数:', queryParams);
    console.log('当前选择的会议室:', state.selectedRoom);
    
    // 如果选择了特定会议室，添加会议室ID到查询参数
    if (state.selectedRoom && state.selectedRoom.id !== null && state.selectedRoom.id !== undefined) {
      queryParams.MeetingRoomId = state.selectedRoom.id;
      console.log('添加会议室ID到查询参数:', queryParams.MeetingRoomId);
    }

    // 更新统计数据
    await getDeviceStatistics();
    
    // 调用设备分页查询API
    const res = await devicesApi.page(queryParams);
    if (res.data && res.data.code === 200) {
      state.tableData = res.data.result.items || [];
      state.tableParams.total = res.data.result.total || 0;
      console.log('设备列表数据更新完成，设备数量:', state.tableData.length);
      console.log('查询到的设备数据:', state.tableData);
      
      // 表格数据更新后，立即从表格数据计算统计信息
      calculateStatisticsFromTableData();
      
      // 如果不是初始加载，更新会议室设备数量
      if (state.roomList.length > 0) {
        await updateRoomDeviceCounts();
      }
    } else {
      state.tableData = [];
      state.tableParams.total = 0;
      // 清空表格数据后，重置统计数据
      calculateStatisticsFromTableData();
    }
  } catch (error) {
    console.error('查询设备列表失败:', error);
    ElMessage.error('查询设备列表失败');
    state.tableData = [];
    state.tableParams.total = 0;
  } finally {
    state.tableLoading = false;
  }
};

// 重置查询条件
const resetQuery = () => {
  state.tableQueryParams = {};
  // handleQuery中已经包含了getDeviceStatistics调用，无需重复调用
  handleQuery();
};

// 列排序
const sortChange = async (column: any) => {
  state.tableParams.field = column.prop;
  state.tableParams.order = column.order;
  await handleQuery();
};

// 删除
const delDevices = (row: any) => {
  ElMessageBox.confirm(`确定要删除吗?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    ElMessage.success("删除成功");
    handleQuery();
  }).catch(() => {});
};

// 批量删除
const batchDelDevices = () => {
  ElMessageBox.confirm(`确定要删除${state.selectData.length}条记录吗?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    ElMessage.success(`成功批量删除${state.selectData.length}条记录`);
    handleQuery();
  }).catch(() => {});
};

// 导出数据
const exportData = () => {
  state.exportLoading = true;
  ElMessage.success("导出功能开发中...");
  setTimeout(() => {
    state.exportLoading = false;
  }, 2000);
};

// 从第三方同步设备
const syncDevicesFromThirdParty = async () => {
  try {
    ElMessage.info("正在从第三方同步设备数据...");
    const res = await devicesApi.SyncFromThirdParty({});
    if (res.data && res.data.code === 200) {
      ElMessage.success("设备同步成功");
      // 同步完成后刷新表格数据
      await handleQuery();
    } else {
      ElMessage.error(res.data?.message || "设备同步失败");
    }
  } catch (error) {
    console.error('同步设备失败:', error);
    ElMessage.error("设备同步失败");
  }
};

// 刷新设备
const refreshDevices = () => {
  ElMessage.info("正在刷新设备状态...");
  handleQuery();
};

// 弹窗引用
const bindDialogRef = ref()
const currentDevice = ref()

// 绑定设备弹窗状态
const bindDialogVisible = ref(false);

// 编辑设备弹窗状态
const editDialogVisible = ref(false);

// 绑定设备
// 替换绑定和编辑方法
const bindDevice = (row: any) => {
  currentDevice.value = row;
  bindDialogVisible.value = true;
};

const editDevice = (row: any) => {
  currentDevice.value = row;
  editDialogVisible.value = true;
};

// 处理绑定确认
const handleBindConfirm = async (data: any) => {
  try {
    bindDialogRef.value?.setLoading(true);
    // 这里可以添加实际的绑定API调用
    ElMessage.success('设备绑定成功');
    bindDialogVisible.value = false;
    await handleQuery();
  } catch (error) {
    console.error('设备绑定失败:', error);
    ElMessage.error('设备绑定失败');
  } finally {
    bindDialogRef.value?.setLoading(false);
  }
};

// 处理编辑确认
const handleEditConfirm = async (data: any) => {
  try {
    editDialogRef.value?.setLoading(true);
    // 这里可以添加实际的编辑API调用
    ElMessage.success('设备编辑成功');
    editDialogVisible.value = false;
    await handleQuery();
  } catch (error) {
    console.error('设备编辑失败:', error);
    ElMessage.error('设备编辑失败');
  } finally {
    editDialogRef.value?.setLoading(false);
  }
};

// 删除设备
const deleteDevice = (row: any) => {
  ElMessageBox.confirm(
    `确定要删除设备 "${row.device_name}" 吗？此操作不可恢复。`,
    '删除确认',
    {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning',
      customClass: 'modern-message-box'
    }
  ).then(async () => {
    try {
      // 这里调用删除API
      // const res = await devicesApi.delete(row.id);
      ElMessage.success('设备删除成功');
      handleQuery();
    } catch (error) {
      ElMessage.error('删除失败，请重试');
    }
  }).catch(() => {
    ElMessage.info('已取消删除');
  });
};

// 确认绑定
const confirmBind = async () => {
  try {
    // 这里调用绑定API
    // const res = await devicesApi.bind(bindForm);
    ElMessage.success('设备绑定成功');
    bindDialogVisible.value = false;
    handleQuery();
  } catch (error) {
    ElMessage.error('绑定失败，请重试');
  }
};

// 确认编辑
const confirmEdit = async () => {
  try {
    // 这里调用编辑API
    // const res = await devicesApi.update(editForm);
    ElMessage.success('设备信息更新成功');
    editDialogVisible.value = false;
    handleQuery();
  } catch (error) {
    ElMessage.error('更新失败，请重试');
  }
};

// 删除cancelBind和cancelEdit方法
// 这些功能现在由新组件内部处理
// 取消编辑
const cancelEdit = () => {
  editDialogVisible.value = false;
  // 重置表单
  Object.assign(editForm, {
    id: '',
    device_name: '',
    staffCode: '',
    roomId: '',
    templateId: '',
    device_type: '',
    mac_address: ''
  });
};

// 查看设备详情
const viewDevice = (row: any) => {
  ElMessage.info(`查看设备详情: ${row.serialNumber}`);
};

// 显示任务对话框
const showTaskDialog = () => {
  ElMessage.info("当前等待任务功能开发中...");
};

// 显示任务等待对话框的状态
const taskDialogVisible = ref(false);
const waitingTasks = ref([
  { id: 1, taskName: '设备同步任务', status: '等待中', createTime: '2024-01-01 12:00:00' },
  { id: 2, taskName: '数据导出任务', status: '处理中', createTime: '2024-01-01 12:05:00' },
  { id: 3, taskName: '状态更新任务', status: '等待中', createTime: '2024-01-01 12:10:00' }
]);

// 处理下拉菜单命令
const handleCommand = (command: string, row: any) => {
  switch (command) {
    case 'print':
      printDialogRef.value.openDialog(row);
      break;
    case 'reset':
      ElMessageBox.confirm(`确定要重置设备 ${row.serialNumber} 吗?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        ElMessage.success("设备重置成功");
      }).catch(() => {});
      break;
    case 'test':
      ElMessage.info(`正在测试设备 ${row.serialNumber} 连接...`);
      break;
    case 'delete':
      delDevices(row);
      break;
  }
};
</script>

<template>
  <div class="devices-container" v-loading="state.exportLoading">
    <div class="layout-container">
      <!-- 左侧会议室列表 -->
      <div class="left-panel">
        <el-card shadow="hover" class="room-card">
          <template #header>
            <div class="card-header">
              <span>会议室列表</span>
              <el-badge :value="state.roomList.length" class="room-count-badge" type="primary" />
            </div>
          </template>
          <div class="room-list" v-loading="state.roomLoading">
            <!-- 所有会议室选项 -->
            <div class="room-item all-rooms" :class="{ active: state.selectedRoom?.id === null }"
              @click="selectRoom({ id: null, name: '所有会议室' })">
              <div class="room-content">
                <div class="room-details">
                  <div class="room-name">所有会议室</div>
                  <div class="room-summary">查看全部设备</div>
                </div>
              </div>
            </div>
            <!-- 会议室列表 -->
            <div v-for="room in state.roomList" :key="room.id" class="room-item"
              :class="{ active: state.selectedRoom?.id === room.id }" @click="selectRoom(room)">
              <div class="room-content">
                <div class="room-details">
                  <div class="room-name">{{ room.name }}</div>
                  <div class="room-info">
                    <span class="device-count">设备: {{ room.deviceCount }}台</span>
                    <span class="status" :class="room.status">{{ room.status === 'online' ? '在线' : '离线' }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 右侧内容区域 -->
      <div class="right-panel">
        <!-- 统计信息面板 -->
        <div class="stats-panel">
          <el-row :gutter="16">
            <el-col :xl="6" :lg="6" :md="12" :sm="12" :xs="24">
              <el-card class="stats-card total-devices">
                <div class="stats-content">
                  <div class="stats-icon">
                    <el-icon size="24">
                      <Monitor />
                    </el-icon>
                  </div>
                  <div class="stats-info">
                    <div class="stats-number">{{ state.statistics.totalDevices }}</div>
                    <div class="stats-label">设备总数</div>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :xl="6" :lg="6" :md="12" :sm="12" :xs="24">
              <el-card class="stats-card online-devices">
                <div class="stats-content">
                  <div class="stats-icon">
                    <el-icon size="24">
                      <Connection />
                    </el-icon>
                  </div>
                  <div class="stats-info">
                    <div class="stats-number">{{ state.statistics.onlineDevices }}</div>
                    <div class="stats-label">在线设备</div>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :xl="6" :lg="6" :md="12" :sm="12" :xs="24">
              <el-card class="stats-card low-battery">
                <div class="stats-content">
                  <div class="stats-icon">
                    <el-icon size="24">
                      <Warning />
                    </el-icon>
                  </div>
                  <div class="stats-info">
                    <div class="stats-number">{{ state.statistics.lowBatteryDevices }}</div>
                    <div class="stats-label">低电量</div>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :xl="6" :lg="6" :md="12" :sm="12" :xs="24">
              <el-card class="stats-card weak-signal">
                <div class="stats-content">
                  <div class="stats-icon">
                    <el-icon size="24">
                      <Close />
                    </el-icon>
                  </div>
                  <div class="stats-info">
                    <div class="stats-number">{{ state.statistics.weakSignalDevices }}</div>
                    <div class="stats-label">信号弱</div>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>


        </div>

        <!-- 查询条件区域 -->
        <el-card shadow="hover" class="query-card">
          <el-form :model="state.tableQueryParams" ref="queryForm" labelWidth="80px" class="query-form">
            <!-- 查询条件行 -->
            <el-row :gutter="20" class="query-row">
              <el-col :xl="5" :lg="6" :md="8" :sm="12" :xs="24">
                <el-form-item label="设备编号" class="compact-form-item">
                  <el-input v-model="state.tableQueryParams.serialNumber" placeholder="请输入设备编号" clearable
                    size="default" />
                </el-form-item>
              </el-col>
              <el-col :xl="4" :lg="5" :md="8" :sm="12" :xs="24">
                <el-form-item label="座位号" class="compact-form-item">
                  <el-input v-model="state.tableQueryParams.seat" placeholder="请输入座位号" clearable size="default" />
                </el-form-item>
              </el-col>
              <el-col :xl="5" :lg="6" :md="8" :sm="12" :xs="24">
                <el-form-item label="设备类别" class="compact-form-item">
                  <el-select v-model="state.tableQueryParams.category" placeholder="请选择类别" clearable size="default"
                    style="width: 100%">
                    <el-option label="姓名桌牌" value="1" />
                    <el-option label="价格标签" value="2" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xl="5" :lg="7" :md="12" :sm="12" :xs="24">
                <el-form-item label="连接状态" class="compact-form-item">
                  <el-select v-model="state.tableQueryParams.result" placeholder="请选择状态" clearable size="default"
                    style="width: 100%">
                    <el-option label="已连接" value="已连接" />
                    <el-option label="连接中" value="连接中" />
                    <el-option label="未连接" value="未连接" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xl="5" :lg="6" :md="12" :sm="12" :xs="24">
                <el-form-item label="信号强度" class="compact-form-item">
                  <el-select v-model="state.tableQueryParams.signal" placeholder="请选择信号" clearable size="default"
                    style="width: 100%">
                    <el-option label="强" value="strong" />
                    <el-option label="中" value="medium" />
                    <el-option label="弱" value="weak" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 操作按钮行 -->
            <el-row :gutter="16" class="operation-button-row">
              <!-- 查询操作区 -->
              <el-col :xl="6" :lg="8" :md="12" :sm="24" :xs="24">
                <div class="button-group query-group">
                  <el-button icon="ele-Search" type="primary" @click="handleQuery" size="default">
                    查询
                  </el-button>
                  <el-button icon="ele-Refresh" @click="resetQuery" size="default">
                    重置
                  </el-button>
                </div>
              </el-col>

              <!-- 功能操作区 -->
              <el-col :xl="12" :lg="10" :md="12" :sm="24" :xs="24">
                <div class="button-group function-group">
                  <el-button type="info" plain @click="showTaskDialog" size="default" icon="ele-Clock">
                    当前等待任务
                    <el-badge :value="3" class="task-badge" />
                  </el-button>
                  <el-button type="warning" plain @click="editDialogRef.openDialog(null, '新增蓝牙桌牌设备')" size="default"
                    icon="ele-Upload">
                    导入设备
                  </el-button>
                  <el-button type="success" plain @click="exportData" size="default" icon="ele-Download">
                    导出设备
                  </el-button>
                  <el-button type="primary" plain @click="syncDevicesFromThirdParty" size="default" icon="ele-Refresh">
                    更新设备
                  </el-button>
                  <!--<el-button type="info" plain @click="refreshDevices" size="default" icon="ele-RefreshRight">
                    刷新列表
                  </el-button>-->
                </div>
              </el-col>

              <!-- 批量操作区 -->
              <el-col :xl="6" :lg="6" :md="24" :sm="24" :xs="24">
                <div class="button-group danger-group">
                  <el-button type="danger" plain @click="batchDelDevices" :disabled="state.selectData.length == 0"
                    size="default" icon="ele-Delete">
                    删除选中 ({{ state.selectData.length }})
                  </el-button>
                </div>
              </el-col>
            </el-row>


          </el-form>
        </el-card>

        <!-- 设备列表表格 -->
        <el-card class="table-card" shadow="hover">
          <div class="table-wrapper">
            <el-table :data="state.tableData" @selection-change="(val: any[]) => { state.selectData = val; }"
              style="width: 100%" v-loading="state.tableLoading" tooltip-effect="light" row-key="id"
              @sort-change="sortChange"
              :header-cell-style="{ background: '#f8f9fa', color: '#495057', fontWeight: 'bold', height: '50px' }"
              :row-style="{ height: '60px' }" :cell-style="{ padding: '12px 0' }" border>
              <el-table-column type="selection" width="40" align="center" />
              <el-table-column type="index" fixed="left" label="序号" width="55" align="center" />
              <el-table-column prop='device_name' align="center" fixed="left" width="150" label='编号'
                show-overflow-tooltip />
              <el-table-column prop='staffCode' label='员工编号' show-overflow-tooltip>
                <template #default="scope">
                  <span>{{ scope.row.staffCode || '-' }}</span>
                </template>
              </el-table-column>
              <el-table-column prop='roomId' label='会议室ID' show-overflow-tooltip>
                <template #default="scope">
                  <span>{{ scope.row.roomId || '-' }}</span>
                </template>
              </el-table-column>
              <el-table-column prop='templateId' label='模板ID' show-overflow-tooltip>
                <template #default="scope">
                  <span>{{ scope.row.templateId || '-' }}</span>
                </template>
              </el-table-column>
              <el-table-column prop='device_type' label='设备类型' show-overflow-tooltip width="120">
                <template #default="scope">
                  <el-tag :type="scope.row.device_type === 7 ? 'primary' : 'success'" effect="light">
                    {{ scope.row.device_type === 7 ? '桌牌设备' : '其他设备' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop='status' label='连接状态' show-overflow-tooltip width="120">
                <template #default="scope">
                  <el-tag :type="scope.row.status ===0 ? 'success' : scope.row.status === 1 ? 'danger' : 'warning'"
                    effect="light">
                    {{ scope.row.status === 0? '已连接' : scope.row.status === 1 ? '未连接' : '异常' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop='signal_strength' label='信号强度' show-overflow-tooltip width="120">
                <template #default="scope">
                  <el-tag
                    v-if="scope.row.signal_strength !== undefined && scope.row.signal_strength !== null"
                    :type="Math.abs(scope.row.signal_strength) <= 50 ? 'success' : Math.abs(scope.row.signal_strength) <= 70 ? 'warning' : 'danger'"
                    effect="light">
                    {{ Math.abs(scope.row.signal_strength) <= 50 ? '强' : Math.abs(scope.row.signal_strength) <=70 ? '中'
                      : '弱' }} ({{ scope.row.signal_strength }}dBm)
                  </el-tag>
                  <span v-else class="text-gray-400">-</span>
                </template>
              </el-table-column>
              <el-table-column prop='mac_address' label='设备MAC' show-overflow-tooltip width="160">
                <template #default="scope">
                  <span style="font-family: 'Courier New', monospace; font-size: 12px;">{{ scope.row.mac_address
                    }}</span>
                </template>
              </el-table-column>
              <el-table-column prop='battery_level' label='电量状态' show-overflow-tooltip width="120">
                <template #default="scope">
                  <el-progress 
                    v-if="scope.row.battery_level !== undefined && scope.row.battery_level !== null"
                    :percentage="scope.row.battery_level" 
                    :stroke-width="12" 
                    :show-text="true"
                    :text-inside="true" 
                    :format="() => scope.row.battery_level + '%'"
                    :color="scope.row.battery_level > 50 ? '#67c23a' : scope.row.battery_level > 20 ? '#e6a23c' : '#f56c6c'" />
                  <span v-else class="text-gray-400">-</span>
                </template>
              </el-table-column>
              <el-table-column prop='apMac' label='AP MAC' show-overflow-tooltip width="160">
                <template #default="scope">
                  <span style="font-family: 'Courier New', monospace; font-size: 12px;">{{ scope.row.apMac || '-' }}</span>
                </template>
              </el-table-column>
              <el-table-column prop='firmware_version' label='固件版本' show-overflow-tooltip width="120">
                <template #default="scope">
                  <span>{{ scope.row.firmware_version || '-' }}</span>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="200" align="center" fixed="right"
                v-if="auth('accessPoints:update') || auth('accessPoints:delete')">
                <template #default="scope">
                  <el-button icon="ele-Link" size="small" text type="success"
                     v-auth="'devices:update'" @click="bindDevice(scope.row)">
                    绑定
                  </el-button>
                  <el-button icon="ele-Edit" size="small" text type="primary"
                     v-auth="'devices:update'" @click="editDevice(scope.row)">
                    编辑
                  </el-button>
                  <el-button icon="ele-Delete" size="small" text type="danger"
                    v-auth="'devices:delete'" @click="deleteDevice(scope.row)">
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="pagination-wrapper">
            <el-pagination v-model:currentPage="state.tableParams.page" v-model:page-size="state.tableParams.pageSize"
              @size-change="(val: any) => handleQuery({ pageSize: val })"
              @current-change="(val: any) => handleQuery({ page: val })"
              layout="total, sizes, prev, pager, next, jumper" :page-sizes="[10, 20, 50, 100, 200, 500]"
              :total="state.tableParams.total" size="small" background />
          </div>
        </el-card>
      </div>
    </div>

    <!-- 绑定设备弹窗 -->
    <BindDeviceDialog
      v-model="bindDialogVisible"
      :device-data="currentDevice"
      :room-list="state.roomList"
      @confirm="handleBindConfirm"
      ref="bindDialogRef"
    />

    <!-- 编辑设备弹窗 -->
    <EditDeviceDialog
      v-model="editDialogVisible"
      :device-data="currentDevice"
      :room-list="state.roomList"
      @confirm="handleEditConfirm"
      ref="editDialogRef"
    />
  </div>
</template>

<style scoped>
/* 主容器样式 */
.devices-container {
  padding: 16px;
  background-color: #f8f9fa;
  height: calc(100vh - 120px);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.layout-container {
  display: flex;
  gap: 16px;
  flex: 1;
  min-height: 0;
  overflow: hidden;
}

/* 左侧面板样式 */
.left-panel {
  width: 300px;
  flex-shrink: 0;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.room-card {
  flex: 1;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  background: #ffffff;
  border: 1px solid #e9ecef;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.room-card :deep(.el-card__header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px 12px 0 0;
  padding: 16px 20px;
  border-bottom: none;
}

.card-header {
  font-weight: 600;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.room-count-badge {
  margin-left: auto;
}

.room-card :deep(.el-card__body) {
  padding: 0;
  flex: 1;
  display: flex;
  flex-direction: column;
  max-height: 80vh;
  overflow: hidden;
}

.room-list {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.room-item {
  padding: 12px 16px;
  margin-bottom: 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #e9ecef;
  background: #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
}

.room-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border-color: #409eff;
}

.room-item.active {
  background: #409eff;
  color: white;
  border-color: #409eff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.room-item.all-rooms {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
  border-color: transparent;
}

.room-item.all-rooms:hover {
  background: linear-gradient(135deg, #f093fb 20%, #f5576c 80%);
}

.room-item.all-rooms.active {
  background: linear-gradient(135deg, #e91e63 0%, #f44336 100%);
}

.room-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.room-details {
  flex: 1;
  min-width: 0;
}

.room-name {
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 4px;
}

.room-summary {
  font-size: 12px;
  opacity: 0.8;
  color: #909399;
}

.room-item.all-rooms .room-summary {
  color: rgba(255, 255, 255, 0.8);
}

.room-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  gap: 8px;
}

.device-count {
  color: #7f8c8d;
  font-weight: 500;
  background: rgba(127, 140, 141, 0.1);
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 11px;
}

.room-item.active .device-count {
  color: white;
  background: rgba(255, 255, 255, 0.2);
}

.status {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
}

.status.online {
  background: #67c23a;
  color: white;
}

.status.offline {
  background: #f56c6c;
  color: white;
}

/* 右侧面板样式 */
.right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
  min-height: 0;
  height: 100%;
  overflow: hidden;
}

/* 统计面板样式 */
.stats-panel {
  flex-shrink: 0;
  margin-bottom: 0;
}

.stats-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.stats-card :deep(.el-card__body) {
  padding: 20px;
  height: 100px;
  display: flex;
  align-items: center;
}

.stats-content {
  display: flex;
  align-items: center;
  gap: 16px;
  width: 100%;
}

.stats-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 24px;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 14px;
  color: #909399;
  font-weight: 500;
}

/* 不同统计卡片的主题色 */
.total-devices {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.total-devices .stats-icon {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.total-devices .stats-label {
  color: rgba(255, 255, 255, 0.8);
}

.online-devices {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.online-devices .stats-icon {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.online-devices .stats-label {
  color: rgba(255, 255, 255, 0.8);
}

.low-battery {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  color: white;
}

.low-battery .stats-icon {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.low-battery .stats-label {
  color: rgba(255, 255, 255, 0.8);
}

.weak-signal {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  color: #333;
}

.weak-signal .stats-icon {
  background: rgba(255, 255, 255, 0.6);
  color: #666;
}

.weak-signal .stats-label {
  color: #666;
}

.query-card {
  flex-shrink: 0;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  background: #ffffff;
  border: 1px solid #e9ecef;
}

.query-card :deep(.el-card__body) {
  padding: 20px;
}

.query-form {
  margin: 0;
  display: flex;
  flex-direction: column;
}

.query-row {
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.query-row .el-col {
  margin-bottom: 12px;
}

.action-row {
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
  margin-top: 8px;
  flex-shrink: 0;
}

.compact-form-item {
  margin-bottom: 0 !important;
}

.compact-form-item :deep(.el-form-item__label) {
  font-size: 14px;
  font-weight: 500;
  color: #606266;
  line-height: 32px;
  padding-bottom: 0;
}

.button-form-item :deep(.el-form-item__label) {
  visibility: hidden;
}

.button-form-item {
  display: flex;
  align-items: flex-end;
}

.button-form-item :deep(.el-form-item__content) {
  width: 100%;
}

.query-buttons {
  display: flex;
  gap: 8px;
  justify-content: flex-start;
  flex-wrap: wrap;
}

.query-buttons .el-button {
  flex: 1;
  min-width: 80px;
}

/* 简化的按钮布局样式 */
.operation-button-row {
  margin-top: 16px;
  margin-bottom: 20px;
}

.button-group {
  display: flex;
  gap: 12px;
  align-items: center;
  padding: 12px 16px;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}

.button-group:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
  border-color: #c0c4cc;
}

/* 按钮组样式 */
.query-group {
  background: rgba(64, 158, 255, 0.05);
  border-color: rgba(64, 158, 255, 0.2);
}

.query-group:hover {
  background: rgba(64, 158, 255, 0.08);
  border-color: rgba(64, 158, 255, 0.3);
}

.function-group {
  justify-content: center;
  flex-wrap: wrap;
}

.danger-group {
  background: rgba(245, 108, 108, 0.05);
  border-color: rgba(245, 108, 108, 0.2);
  justify-content: center;
}

.danger-group:hover {
  background: rgba(245, 108, 108, 0.08);
  border-color: rgba(245, 108, 108, 0.3);
}

/* 按钮样式优化 */
.button-group .el-button {
  height: 36px;
  border-radius: 6px;
  font-size: 14px;
  padding: 0 16px;
  transition: all 0.3s ease;
  font-weight: 500;
}

.query-group .el-button {
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.1);
}

.query-group .el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(64, 158, 255, 0.15);
}

.function-group .el-button {
  min-width: 100px;
}

.danger-group .el-button {
  min-width: 120px;
}

/* 任务徽章样式 */
.task-badge {
  margin-left: 8px;
}

.task-badge :deep(.el-badge__content) {
  background-color: #f56c6c;
  border: 1px solid #fff;
  font-size: 10px;
  height: 16px;
  line-height: 16px;
  min-width: 16px;
  padding: 0 4px;
}


.query-btn:hover::before {
  left: 100%;
}

.query-btn:hover {
  transform: translateY(-2px);
  box-shadow:
    0 6px 16px rgba(64, 158, 255, 0.3),
    0 2px 8px rgba(64, 158, 255, 0.2);
}

.query-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.2);
}

.reset-btn {
  min-width: 100px;
  height: 36px;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border: 1px solid #d9d9d9;
  color: #666666;
  position: relative;
  overflow: hidden;
}

.reset-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(64, 158, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.reset-btn:hover::before {
  left: 100%;
}

.reset-btn:hover {
  transform: translateY(-2px);
  box-shadow:
    0 6px 16px rgba(0, 0, 0, 0.1),
    0 2px 8px rgba(0, 0, 0, 0.06);
  border-color: #409eff;
  color: #409eff;
  background: linear-gradient(135deg, #ffffff 0%, #f0f8ff 100%);
}

.reset-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.action-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
}

.action-buttons-left {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.action-buttons-right {
  display: flex;
  gap: 12px;
}

/* 任务徽章样式 */
.task-badge {
  margin-left: 8px;
}

.task-badge :deep(.el-badge__content) {
  background-color: #f56c6c;
  border: 1px solid #fff;
  font-size: 10px;
  height: 16px;
  line-height: 16px;
  min-width: 16px;
  padding: 0 4px;
}

/* 操作按钮组样式 */
.operation-buttons {
  display: flex;
  align-items: center;
  gap: 4px;
  justify-content: center;
}

.operation-buttons .el-button {
  margin: 0;
  padding: 4px 8px;
  font-size: 12px;
}

.operation-buttons .el-dropdown {
  margin-left: 4px;
}

/* 表格样式 */
.table-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  background: #ffffff;
  border: 1px solid #e9ecef;
  min-height: 0;
  overflow: hidden;
}

.table-card :deep(.el-card__body) {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 20px;
  min-height: 0;
  overflow: hidden;
}

.table-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow: hidden;
}

.table-wrapper :deep(.el-table) {
  flex: 1;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  display: flex;
  flex-direction: column;
}

.table-wrapper :deep(.el-table .el-table__header-wrapper) {
  flex-shrink: 0;
}

.table-wrapper :deep(.el-table .el-table__body-wrapper) {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
}

.pagination-wrapper {
  flex-shrink: 0;
  padding-top: 16px;
  display: flex;
  justify-content: center;
}

.battery-display {
  display: flex;
  align-items: center;
  gap: 8px;
}

.battery-text {
  font-size: 12px;
  font-weight: 600;
  min-width: 35px;
}



/* 现代化弹窗样式 */
.modern-dialog {
  border-radius: 16px;
  overflow: hidden;
}

.modern-dialog :deep(.el-dialog) {
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  background: #ffffff;
  overflow: hidden;
}

.modern-dialog :deep(.el-dialog__header) {
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-bottom: 1px solid #e9ecef;
  padding: 20px 24px 16px;
  margin: 0;
}

.modern-dialog :deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.modern-dialog :deep(.el-dialog__body) {
  padding: 24px;
  background: #ffffff;
}

.modern-dialog :deep(.el-dialog__footer) {
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
  padding: 16px 24px;
  margin: 0;
}

.dialog-content {
  padding: 0;
}

.dialog-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 12px;
  border: 1px solid #e9ecef;
}

.dialog-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

.dialog-title-text h3 {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.dialog-title-text p {
  margin: 0;
  font-size: 14px;
  color: #6c757d;
}

.modern-form {
  margin-top: 0;
}

.modern-form :deep(.el-form-item__label) {
  font-weight: 500;
  color: #495057;
  font-size: 14px;
}

.modern-form :deep(.el-input__wrapper) {
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}

.modern-form :deep(.el-input__wrapper:hover) {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
}

.modern-form :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
}

.modern-form :deep(.el-select .el-input__wrapper) {
  border-radius: 8px;
}

.disabled-input :deep(.el-input__wrapper) {
  background: #f8f9fa;
  border-color: #e9ecef;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 0;
}

.cancel-btn {
  height: 40px;
  padding: 0 20px;
  border-radius: 8px;
  font-weight: 500;
  background: #ffffff;
  border: 1px solid #d9d9d9;
  color: #666666;
  transition: all 0.3s ease;
}

.cancel-btn:hover {
  background: #f5f5f5;
  border-color: #b3b3b3;
  color: #333333;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.confirm-btn {
  height: 40px;
  padding: 0 20px;
  border-radius: 8px;
  font-weight: 500;
  background: linear-gradient(135deg, #409eff 0%, #3a8ee6 100%);
  border: none;
  transition: all 0.3s ease;
}

.confirm-btn:hover {
  background: linear-gradient(135deg, #3a8ee6 0%, #337ecc 100%);
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(64, 158, 255, 0.3);
}

/* 删除确认框样式 */
:deep(.modern-message-box) {
  border-radius: 12px;
  box-shadow: 0 16px 32px rgba(0, 0, 0, 0.15);
}

:deep(.modern-message-box .el-message-box__header) {
  background: linear-gradient(135deg, #fff5f5 0%, #ffffff 100%);
  border-bottom: 1px solid #fecaca;
  padding: 20px 24px 16px;
}

:deep(.modern-message-box .el-message-box__title) {
  color: #dc2626;
  font-weight: 600;
}

/* 数据缺失样式 */
.text-gray-400 {
  color: #9ca3af;
  font-style: italic;
  font-size: 13px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .modern-dialog :deep(.el-dialog) {
    width: 95% !important;
    margin: 0 auto;
  }
  
  .dialog-header {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }
  
  .modern-form :deep(.el-row) {
    flex-direction: column;
  }
  
  .modern-form :deep(.el-col) {
    width: 100% !important;
    margin-bottom: 16px;
  }
}



/* 额外的响应式设计 */
@media (max-width: 1200px) {
  .devices-container {
    height: calc(100vh - 100px);
  }

  .layout-container {
    flex-direction: column;
    gap: 12px;
  }

  .left-panel {
    width: 100%;
    height: auto;
    flex-shrink: 0;
  }

  .right-panel {
    flex: 1;
    min-height: 0;
  }

  .room-card {
    max-height: 300px;
  }

  .query-buttons {
    justify-content: center;
    gap: 12px;
  }

  .query-buttons .el-button {
    min-width: 100px;
    flex: 0 0 auto;
  }
}

@media (max-width: 992px) {
  .button-group {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .button-group .el-button {
    height: 36px;
    font-size: 13px;
  }
}
</style>
