﻿import {useBaseApi} from '/@/api/base';

// 会议室表接口服务
export const useMeetingRoomsApi = () => {
	const baseApi = useBaseApi("meetingRooms");
	return {
		// 分页查询会议室表
		page: baseApi.page,
		// 查看会议室表详细
		detail: baseApi.detail,
		// 新增会议室表
		add: baseApi.add,
		// 更新会议室表
		update: baseApi.update,
		// 删除会议室表
		delete: baseApi.delete,
		// 批量删除会议室表
		batchDelete: baseApi.batchDelete,
	}
}

// 会议室表实体
export interface MeetingRooms {
	// 主键Id
	id: number;
	// 房间名称
	room_name: string;
	// 房间编号
	room_code: string;
	// 房间位置
	room_location: string;
	// 会议室容量
	capacity: number;
	// 绑定设备
	equipment: string;
	// 会议室状态
	meeting_status: number;
	// 软删除
	isDelete?: boolean;
	// 创建时间
	createTime: string;
	// 更新时间
	updateTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
}