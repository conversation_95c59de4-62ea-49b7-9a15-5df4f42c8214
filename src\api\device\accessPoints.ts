import {useBase<PERSON><PERSON>} from '/@/api/base';
import request from '/@/utils/request';

// 接入点/网关表接口服务
export const useAccessPointsApi = () => {
	const baseApi = useBaseApi("accessPoints");
	return {
		// 分页查询接入点/网关表
		page: baseApi.page,
		// 查看接入点/网关表详细
		detail: baseApi.detail,
		// 新增接入点/网关表
		add: baseApi.add,
		// 更新接入点/网关表
		update: baseApi.update,
		// 删除接入点/网关表
		delete: baseApi.delete,
		// 批量删除接入点/网关表
		batchDelete: baseApi.batchDelete,
		// 同步数据
		syncData: baseApi.syncData,
		//网关命令下发
		command: function (data: any, cancel: boolean = false) {
			return request({
				url: baseApi.baseUrl + 'command',
				method: 'post',
				data
			}, cancel);
		}
	}
}

// 接入点/网关表实体
export interface AccessPoints {
	// 主键Id
	id: number;
	// AP名称
	ap_name: string;
	// 物理地址
	mac_address: string;
	// IP地址
	ip_address: string;
	// ap地址
	ap_location: string;
	// ap状态1：在线；2：离线；3：异常；
	ap_status: number;
	// 固件版本
	firmware_version: string;
	// 信号强度
	signal_strength: number;
	// 链接设备连接数
	connected_devices_count: number;
	// 最大链接数
	max_devices: number;
	// 软删除
	isDelete?: boolean;
	// 创建时间
	createTime: string;
	// 更新时间
	updateTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
}