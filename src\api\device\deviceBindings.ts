﻿import {useBaseApi} from '/@/api/base';

// 设备绑定表接口服务
export const useDeviceBindingsApi = () => {
	const baseApi = useBaseApi("deviceBindings");
	return {
		// 分页查询设备绑定表
		page: baseApi.page,
		// 查看设备绑定表详细
		detail: baseApi.detail,
		// 新增设备绑定表
		add: baseApi.add,
		// 更新设备绑定表
		update: baseApi.update,
		// 删除设备绑定表
		delete: baseApi.delete,
		// 批量删除设备绑定表
		batchDelete: baseApi.batchDelete,
	}
}

// 设备绑定表实体
export interface DeviceBindings {
	// 主键Id
	id: number;
	// 设备ID
	device_id: number;
	// 员工ID
	staff_id: number;
	// 模板ID
	template_id: number;
	// 会议室ID
	meeting_room_id: number;
	// 绑定类型
	binding_type: number;
	// 绑定数据
	binding_data: string;
	// 软删除
	isDelete?: boolean;
	// 创建时间
	createTime: string;
	// 更新时间
	updateTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
}