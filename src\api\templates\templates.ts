﻿import {useBaseApi} from '/@/api/base';

// 模板管理模块接口服务
export const useTemplatesApi = () => {
	const baseApi = useBaseApi("templates");
	return {
		// 分页查询模板管理模块
		page: baseApi.page,
		// 查看模板管理模块详细
		detail: baseApi.detail,
		// 新增模板管理模块
		add: baseApi.add,
		// 更新模板管理模块
		update: baseApi.update,
		// 删除模板管理模块
		delete: baseApi.delete,
		// 批量删除模板管理模块
		batchDelete: baseApi.batchDelete,
	}
}

// 模板管理模块实体
export interface Templates {
	// 主键Id
	id: number;
	// 模版名称
	template_name: string;
	// 1：姓名牌；2：会议牌；3：自定义牌
	template_type: number;
	// 设计内容
	design_data: string;
	// 预览图片
	preview_image: string;
	// 宽度
	width: number;
	// 高度
	height: number;
	// 是否默认
	is_default: boolean;
	// 是否启用
	template_status: number;
	// 软删除
	isDelete?: boolean;
	// 创建时间
	createTime: string;
	// 更新时间
	updateTime: string;
	// 创建者Id
	createUserId: number;
	// 创建者姓名
	createUserName: string;
	// 修改者Id
	updateUserId: number;
	// 修改者姓名
	updateUserName: string;
}