<template>
  <el-dialog
    v-model="visible"
    title="绑定设备"
    width="600px"
    :close-on-click-modal="false"
    class="modern-dialog"
    align-center
    @close="handleClose"
  >
    <div class="dialog-content">
      <div class="dialog-header">
        <el-icon class="dialog-icon" size="24" color="#67c23a">
          <ele-Link />
        </el-icon>
        <div class="dialog-title-text">
          <h3>设备绑定配置</h3>
          <p>为设备配置人员信息和模板信息</p>
        </div>
      </div>
      
      <el-form 
        ref="bindFormRef"
        :model="formData" 
        :rules="rules"
        label-width="100px"
        class="modern-form"
      >
        <!-- 隐藏的设备ID字段 -->
        <el-form-item label="" prop="deviceId" style="display: none;">
          <el-input v-model="formData.deviceId" />
        </el-form-item>
        
        <el-row :gutter="24" class="form-row">
          <el-col :span="12">
            <el-form-item label="设备名称" prop="deviceName">
              <el-input 
                v-model="formData.deviceName" 
                placeholder="设备名称"
                readonly
                class="disabled-input"
              >
                <template #prefix>
                  <el-icon><ele-Monitor /></el-icon>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备MAC" prop="deviceMac">
              <el-input 
                v-model="formData.deviceMac" 
                placeholder="设备MAC地址"
                readonly
                class="disabled-input"
              >
                <template #prefix>
                  <el-icon><ele-Connection /></el-icon>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="24" class="form-row">
          <el-col :span="12">
            <el-form-item label="工位号" prop="workstationCode">
              <el-select 
                v-model="formData.workstationCode" 
                placeholder="请选择工位号"
                filterable
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="item in workstationOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                  <span class="workstation-option">{{ item.label }}</span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="模版编号" prop="templateCode">
              <el-select 
                v-model="formData.templateCode" 
                placeholder="请选择模版编号"
                filterable
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="item in templateOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                  <span class="template-option">{{ item.label }}</span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="24" class="form-row">
          <el-col :span="12">
            <el-form-item label="处理算法" prop="algorithm">
              <el-select 
                v-model="formData.algorithm" 
                placeholder="请选择处理算法"
                filterable
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="item in algorithmOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                  <span class="algorithm-option">{{ item.label }}</span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="网关MAC" prop="gatewayMac">
              <el-input 
                v-model="formData.gatewayMac" 
                placeholder="网关MAC地址"
                readonly
                class="disabled-input"
              >
                <template #prefix>
                  <el-icon><ele-Router /></el-icon>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    
    <template #footer>
      <div class="dialog-footer" ref="footerRef">
        <el-button @click="handleRefreshA" class="refresh-btn refresh-a" :loading="loading">
          <el-icon><ele-Refresh /></el-icon>
          A面刷新
        </el-button>
        <el-button @click="handleRefreshB" class="refresh-btn refresh-b" :loading="loading">
          <el-icon><ele-Refresh /></el-icon>
          B面刷新
        </el-button>
        <el-button 
          type="primary" 
          @click="handleRefreshBoth" 
          class="refresh-btn refresh-both"
          :loading="loading"
        >
          <el-icon><ele-Refresh /></el-icon>
          双面刷新
        </el-button>
        <el-button @click="handleClose" class="cancel-btn" :loading="loading">
          <el-icon><ele-Close /></el-icon>
          取消
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'

interface Room {
  id: string
  name: string
}

interface BindForm {
  deviceId: string
  deviceName: string
  deviceMac: string
  workstationCode: string
  templateCode: string
  algorithm: string
  gatewayMac: string
}

interface Props {
  modelValue: boolean
  deviceData?: any
  roomList: Room[]
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm', data: BindForm): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = ref(false)
const loading = ref(false)
const bindFormRef = ref()
const footerRef = ref()

// 拖拽相关变量
let isDragging = false
let startX = 0
let startY = 0
let initialX = 0
let initialY = 0

// 下拉框选项数据
const workstationOptions = ref([
  { value: 'WS001', label: '工位001' },
  { value: 'WS002', label: '工位002' },
  { value: 'WS003', label: '工位003' },
  { value: 'WS004', label: '工位004' },
  { value: 'WS005', label: '工位005' }
])

const templateOptions = ref([
  { value: 'TPL001', label: '模版001' },
  { value: 'TPL002', label: '模版002' },
  { value: 'TPL003', label: '模版003' },
  { value: 'TPL004', label: '模版004' },
  { value: 'TPL005', label: '模版005' }
])

const algorithmOptions = ref([
  { value: 'ALG001', label: '算法001' },
  { value: 'ALG002', label: '算法002' },
  { value: 'ALG003', label: '算法003' },
  { value: 'ALG004', label: '算法004' },
  { value: 'ALG005', label: '算法005' }
])

const formData = reactive<BindForm>({
  deviceId: '',
  deviceName: '',
  deviceMac: '',
  workstationCode: '',
  templateCode: '',
  algorithm: '',
  gatewayMac: ''
})

const rules = {
  deviceId: [],
  deviceName: [],
  deviceMac: [],
  workstationCode: [
    { required: true, message: '请选择工位号', trigger: 'change' }
  ],
  templateCode: [
    { required: true, message: '请选择模版编号', trigger: 'change' }
  ],
  algorithm: [
    { required: true, message: '请选择处理算法', trigger: 'change' }
  ],
  gatewayMac: []
}

watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal && props.deviceData) {
    nextTick(() => {
      formData.deviceId = props.deviceData.id || ''
      formData.deviceName = props.deviceData.device_name || ''
      formData.deviceMac = props.deviceData.mac_address || ''
      formData.workstationCode = props.deviceData.staffCode || ''
      formData.templateCode = props.deviceData.templateId || ''
      formData.algorithm = props.deviceData.algorithm || ''
      formData.gatewayMac = props.deviceData.apMac || ''
    })
  }
})

const handleClose = () => {
  visible.value = false
  emit('update:modelValue', false)
  resetForm()
}

const resetForm = () => {
  nextTick(() => {
    bindFormRef.value?.resetFields()
    Object.assign(formData, {
      deviceId: '',
      deviceName: '',
      deviceMac: '',
      workstationCode: '',
      templateCode: '',
      algorithm: '',
      gatewayMac: ''
    })
  })
}

// A面刷新
const handleRefreshA = async () => {
  try {
    await bindFormRef.value?.validate()
    loading.value = true
    ElMessage.success('A面刷新成功')
    emit('confirm', { ...formData, refreshType: 'A' })
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// B面刷新
const handleRefreshB = async () => {
  try {
    await bindFormRef.value?.validate()
    loading.value = true
    ElMessage.success('B面刷新成功')
    emit('confirm', { ...formData, refreshType: 'B' })
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 双面刷新
const handleRefreshBoth = async () => {
  try {
    await bindFormRef.value?.validate()
    loading.value = true
    ElMessage.success('双面刷新成功')
    emit('confirm', { ...formData, refreshType: 'BOTH' })
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const setLoading = (status: boolean) => {
  loading.value = status
}

// 拖拽功能实现
const handleMouseDown = (e: MouseEvent) => {
  isDragging = true
  startX = e.clientX
  startY = e.clientY
  
  const dialogElement = document.querySelector('.modern-dialog .el-dialog') as HTMLElement
  if (dialogElement) {
    const rect = dialogElement.getBoundingClientRect()
    initialX = rect.left
    initialY = rect.top
    dialogElement.style.transition = 'none'
  }
  
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
  e.preventDefault()
}

const handleMouseMove = (e: MouseEvent) => {
  if (!isDragging) return
  
  const deltaX = e.clientX - startX
  const deltaY = e.clientY - startY
  
  const dialogElement = document.querySelector('.modern-dialog .el-dialog') as HTMLElement
  if (dialogElement) {
    const newX = initialX + deltaX
    const newY = initialY + deltaY
    
    // 限制拖拽范围，防止拖出屏幕
    const maxX = window.innerWidth - dialogElement.offsetWidth
    const maxY = window.innerHeight - dialogElement.offsetHeight
    
    const clampedX = Math.max(0, Math.min(newX, maxX))
    const clampedY = Math.max(0, Math.min(newY, maxY))
    
    dialogElement.style.left = clampedX + 'px'
    dialogElement.style.top = clampedY + 'px'
    dialogElement.style.margin = '0'
  }
}

const handleMouseUp = () => {
  isDragging = false
  
  const dialogElement = document.querySelector('.modern-dialog .el-dialog') as HTMLElement
  if (dialogElement) {
    dialogElement.style.transition = 'all 0.3s ease'
  }
  
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleMouseUp)
}

// 生命周期钩子
onMounted(() => {
  // 为footer添加拖拽功能
  nextTick(() => {
    if (footerRef.value) {
      footerRef.value.addEventListener('mousedown', handleMouseDown)
      footerRef.value.style.cursor = 'move'
    }
    
    // 设置对话框初始位置为屏幕中央
    const dialogElement = document.querySelector('.modern-dialog .el-dialog') as HTMLElement
    if (dialogElement) {
      const rect = dialogElement.getBoundingClientRect()
      const centerX = (window.innerWidth - rect.width) / 2
      const centerY = (window.innerHeight - rect.height) / 2
      
      dialogElement.style.left = centerX + 'px'
      dialogElement.style.top = centerY + 'px'
      dialogElement.style.margin = '0'
    }
  })
})

onUnmounted(() => {
  if (footerRef.value) {
    footerRef.value.removeEventListener('mousedown', handleMouseDown)
  }
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleMouseUp)
})

defineExpose({
  setLoading,
  resetForm
})
</script>

<style scoped>
/* 现代化弹窗样式 */
.modern-dialog {
  border-radius: 16px;
  overflow: hidden;
}

.modern-dialog :deep(.el-dialog) {
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  background: #ffffff;
  overflow: hidden;
  position: fixed !important;
  margin: 0 !important;
  transform: none !important;
}

.modern-dialog :deep(.el-dialog__header) {
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-bottom: 1px solid #e9ecef;
  padding: 20px 24px 16px;
  margin: 0;
}

.modern-dialog :deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.modern-dialog :deep(.el-dialog__body) {
  padding: 24px;
  background: #ffffff;
}

.modern-dialog :deep(.el-dialog__footer) {
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
  padding: 16px 24px;
  margin: 0;
}

.dialog-content {
  padding: 0;
}

.dialog-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 12px;
  border: 1px solid #e9ecef;
}

.dialog-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

.dialog-title-text h3 {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.dialog-title-text p {
  margin: 0;
  font-size: 14px;
  color: #6c757d;
}

.modern-form {
  margin-top: 0;
}

.modern-form :deep(.el-form-item) {
  margin-bottom: 20px;
}

.modern-form :deep(.el-form-item__label) {
  font-weight: 500;
  color: #495057;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 8px;
}

.form-row {
  margin-bottom: 16px;
}

.form-row:last-child {
  margin-bottom: 0;
}

.modern-form :deep(.el-input__wrapper) {
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}

.modern-form :deep(.el-input__wrapper:hover) {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
}

.modern-form :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
}

.modern-form :deep(.el-select .el-input__wrapper) {
  border-radius: 8px;
}

.disabled-input :deep(.el-input__wrapper) {
  background: #f8f9fa;
  border-color: #e9ecef;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 0;
  cursor: move;
  user-select: none;
}

.cancel-btn {
  height: 40px;
  padding: 0 20px;
  border-radius: 8px;
  font-weight: 500;
  background: #ffffff;
  border: 1px solid #d9d9d9;
  color: #666666;
  transition: all 0.3s ease;
}

.cancel-btn:hover {
  background: #f5f5f5;
  border-color: #b3b3b3;
  color: #333333;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.confirm-btn {
  height: 40px;
  padding: 0 20px;
  border-radius: 8px;
  font-weight: 500;
  background: linear-gradient(135deg, #409eff 0%, #3a8ee6 100%);
  border: none;
  transition: all 0.3s ease;
}

.refresh-btn {
  height: 40px;
  padding: 0 20px;
  border-radius: 8px;
  font-weight: 500;
  border: 1px solid #d9d9d9;
  transition: all 0.3s ease;
}

.refresh-a {
  background: linear-gradient(135deg, #67c23a 0%, #5daf34 100%);
  color: white;
  border-color: #67c23a;
}

.refresh-a:hover {
  background: linear-gradient(135deg, #5daf34 0%, #529b2e 100%);
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(103, 194, 58, 0.3);
}

.refresh-b {
  background: linear-gradient(135deg, #e6a23c 0%, #cf9236 100%);
  color: white;
  border-color: #e6a23c;
}

.refresh-b:hover {
  background: linear-gradient(135deg, #cf9236 0%, #b8833d 100%);
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(230, 162, 60, 0.3);
}

.refresh-both {
  background: linear-gradient(135deg, #409eff 0%, #3a8ee6 100%);
  border: none;
}

.refresh-both:hover {
  background: linear-gradient(135deg, #3a8ee6 0%, #337ecc 100%);
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(64, 158, 255, 0.3);
}

.workstation-option,
.template-option,
.algorithm-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

@media (max-width: 768px) {
  .modern-dialog :deep(.el-dialog) {
    width: 95% !important;
    margin: 0 auto;
  }
  
  .dialog-header {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }
  
  .modern-form :deep(.el-row) {
    flex-direction: column;
  }
  
  .modern-form :deep(.el-col) {
    width: 100% !important;
    margin-bottom: 16px;
  }
}
</style>