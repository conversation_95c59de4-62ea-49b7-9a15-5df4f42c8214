﻿<script lang="ts" name="accessPoints" setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";
import type { FormRules } from "element-plus";
import { formatDate } from '/@/utils/formatTime';
import { useAccessPointsApi } from '/@/api/device/accessPoints';

//父级传递来的函数，用于回调
const emit = defineEmits(["reloadTable"]);
const accessPointsApi = useAccessPointsApi();
const ruleFormRef = ref();

const state = reactive({
	title: '',
	loading: false,
	showDialog: false,
	ruleForm: {} as any,
	stores: {},
	dropdownData: {} as any,
});

// MAC地址格式化函数
const formatMacAddress = (value: string) => {
	// 移除所有非十六进制字符
	let cleaned = value.replace(/[^0-9A-Fa-f]/g, '');
	// 限制最大长度为12个字符
	if (cleaned.length > 12) {
		cleaned = cleaned.substring(0, 12);
	}
	// 每两个字符添加冒号
	const formatted = cleaned.replace(/(\w{2})/g, '$1:').replace(/:$/, '');
	return formatted.toUpperCase();
};

// MAC地址输入处理函数
const handleMacInput = (event: Event) => {
	const target = event.target as HTMLInputElement;
	const cursorPosition = target.selectionStart || 0;
	const oldValue = target.value;
	const newValue = formatMacAddress(target.value);
	
	// 更新值
	state.ruleForm.mac_address = newValue;
	
	// 调整光标位置
	setTimeout(() => {
		const newCursorPosition = cursorPosition + (newValue.length - oldValue.length);
		target.setSelectionRange(newCursorPosition, newCursorPosition);
	}, 0);
};

// MAC地址粘贴处理函数
const handleMacPaste = (event: ClipboardEvent) => {
	event.preventDefault();
	const pastedText = event.clipboardData?.getData('text') || '';
	const formattedValue = formatMacAddress(pastedText);
	state.ruleForm.mac_address = formattedValue;
	
	// 触发验证
	setTimeout(() => {
		ruleFormRef.value?.validateField('mac_address');
	}, 100);
};

// MAC地址格式验证函数
const validateMacAddress = (rule: any, value: string, callback: any) => {
	if (!value) {
		callback();
		return;
	}
	// MAC地址正则表达式：支持 XX:XX:XX:XX:XX:XX 和 XX-XX-XX-XX-XX-XX 格式
	const macRegex = /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/;
	if (!macRegex.test(value)) {
		callback(new Error('MAC地址格式不正确，请输入正确的格式（如：AA:BB:CC:DD:EE:FF）'));
	} else {
		callback();
	}
};

// 自行添加其他规则
const rules = ref<FormRules>({
	mac_address: [
		{ required: true, message: '请输入物理地址', trigger: 'blur' },
		{ validator: validateMacAddress, trigger: 'blur' }
	]
});

// 页面加载时
onMounted(async () => {
});

// 打开弹窗
const openDialog = async (row: any, title: string) => {
	state.title = title;
	row = row ?? {  };
	state.ruleForm = row.id ? await accessPointsApi.detail(row.id).then(res => res.data.result) : JSON.parse(JSON.stringify(row));
	state.showDialog = true;
};

// 关闭弹窗
const closeDialog = () => {
	emit("reloadTable");
	state.showDialog = false;
};

// 提交
const submit = async () => {
	ruleFormRef.value.validate(async (isValid: boolean, fields?: any) => {
		if (isValid) {
			let values = state.ruleForm;
			await accessPointsApi[state.ruleForm.id ? 'update' : 'add'](values);
			closeDialog();
		} else {
			ElMessage({
				message: `表单有${Object.keys(fields).length}处验证失败，请修改后再提交`,
				type: "error",
			});
		}
	});
};

//将属性或者函数暴露给父组件
defineExpose({ openDialog });
</script>
<template>
	<div class="accessPoints-container">
		<el-dialog v-model="state.showDialog" draggable :close-on-click-modal="false">
			<template #header>
				<div style="color: #fff">
					<span>{{ state.title }}</span>
				</div>
			</template>
			<el-form :model="state.ruleForm" ref="ruleFormRef" label-width="auto" :rules="rules">
				<el-row :gutter="35">
					<el-form-item v-show="false">
						<el-input v-model="state.ruleForm.id" />
					</el-form-item>
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20" >
						<el-form-item label="AP名称" prop="ap_name">
							<el-input v-model="state.ruleForm.ap_name" placeholder="请输入AP名称" maxlength="200" show-word-limit clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20" >
						<el-form-item label="物理地址" prop="mac_address">
							<el-input 
								v-model="state.ruleForm.mac_address" 
								placeholder="请输入MAC地址（如：AA:BB:CC:DD:EE:FF）" 
								maxlength="17" 
								show-word-limit 
								clearable 
								@input="handleMacInput"
								@paste="handleMacPaste"
								style="text-transform: uppercase;"
							/>
							<div style="font-size: 12px; color: #909399; margin-top: 4px;">
								支持格式：AA:BB:CC:DD:EE:FF 或 AA-BB-CC-DD-EE-FF，系统会自动格式化
							</div>
						</el-form-item>
					</el-col> 
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="() => state.showDialog = false">取 消</el-button>
					<el-button @click="submit" type="primary" v-reclick="1000">确 定</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>
<style lang="scss" scoped>
:deep(.el-select), :deep(.el-input-number) {
  width: 100%;
}
</style>