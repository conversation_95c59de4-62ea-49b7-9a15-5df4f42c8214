<template>
  <el-dialog v-model="visible" title="编辑设备" width="700px" :close-on-click-modal="false" class="modern-dialog"
    align-center @close="handleClose">
    <div class="dialog-content">
      <div class="dialog-header">
        <el-icon class="dialog-icon" size="24" color="#409eff">
          <ele-Edit />
        </el-icon>
        <div class="dialog-title-text">
          <h3>编辑设备信息</h3>
          <p>修改设备的基本信息和配置</p>
        </div>
      </div>

      <el-form ref="editFormRef" :model="formData" :rules="rules" label-width="120px" class="modern-form">
        <!-- 隐藏的设备ID字段 -->
        <el-form-item label="" prop="deviceId" style="display: none;">
          <el-input v-model="formData.deviceId" />
        </el-form-item>

        <el-row :gutter="24" class="form-row">
          <el-col :span="24">
            <el-form-item label="设备终端MAC" prop="terminalMac">
              <el-input v-model="formData.terminalMac" placeholder="请输入设备终端MAC地址" maxlength="17" show-word-limit>
                <template #prefix>
                  <el-icon><ele-Connection /></el-icon>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="24" class="form-row">
          <el-col :span="24">
            <el-form-item label="会议室编号" prop="roomId">
              <el-select v-model="formData.roomId" placeholder="请选择会议室" style="width: 100%" class="room-select">
                <el-option v-for="room in roomOptions" :key="room.value" :label="room.label" :value="room.value">
                  <div class="room-option">
                    <el-icon><ele-OfficeBuilding /></el-icon>
                    <span>{{ room.label }}</span>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>

        </el-row>
        <el-row :gutter="24" class="form-row">
          <el-col :span="24">
            <el-form-item label="座位号" prop="seatNumber">
              <el-input v-model="formData.seatNumber" placeholder="请输入座位号" maxlength="10" show-word-limit>
                <template #prefix>
                  <el-icon><ele-User /></el-icon>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" class="cancel-btn" :loading="loading">
          <el-icon><ele-Close /></el-icon>
          取消
        </el-button>
        <el-button type="primary" @click="handleConfirm" class="confirm-btn" :loading="loading">
          <el-icon><ele-Check /></el-icon>
          保存修改
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'

interface Room {
  id: string
  name: string
}

interface EditForm {
  deviceId: string
  terminalMac: string
  roomId: string
  seatNumber: string
}

interface Props {
  modelValue: boolean
  deviceData?: any
  roomList: Room[]
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm', data: EditForm): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = ref(false)
const loading = ref(false)
const editFormRef = ref()

const formData = reactive<EditForm>({
  deviceId: '',
  terminalMac: '',
  roomId: '',
  seatNumber: ''
})

// 会议室选项数据
const roomOptions = ref([
  { value: 'ROOM001', label: '会议室001' },
  { value: 'ROOM002', label: '会议室002' },
  { value: 'ROOM003', label: '会议室003' },
  { value: 'ROOM004', label: '会议室004' },
  { value: 'ROOM005', label: '会议室005' }
])

const rules = {
  terminalMac: [
    { required: true, message: '请输入设备终端MAC地址', trigger: 'blur' },
    { 
      pattern: /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/,
      message: '请输入正确的MAC地址格式',
      trigger: 'blur'
    }
  ],
  roomId: [
    { required: true, message: '请选择会议室', trigger: 'change' }
  ],
  seatNumber: [
    { required: true, message: '请输入座位号', trigger: 'blur' },
    { min: 1, max: 10, message: '长度在 1 到 10 个字符', trigger: 'blur' }
  ]
}

watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal && props.deviceData) {
    nextTick(() => {
      formData.deviceId = props.deviceData.id || ''
      formData.terminalMac = props.deviceData.mac_address || props.deviceData.terminalMac || ''
      formData.roomId = props.deviceData.roomId || ''
      formData.seatNumber = props.deviceData.seatNumber || ''
    })
  }
})

const handleClose = () => {
  visible.value = false
  emit('update:modelValue', false)
  resetForm()
}

const resetForm = () => {
  nextTick(() => {
    editFormRef.value?.resetFields()
    Object.assign(formData, {
      deviceId: '',
      terminalMac: '',
      roomId: '',
      seatNumber: ''
    })
  })
}

const handleConfirm = async () => {
  try {
    await editFormRef.value?.validate()
    loading.value = true
    emit('confirm', { ...formData })
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const setLoading = (status: boolean) => {
  loading.value = status
}

defineExpose({
  setLoading,
  resetForm
})
</script>

<style scoped>
/* 现代化弹窗样式 */
.modern-dialog {
  border-radius: 16px;
  overflow: hidden;
}

.modern-dialog :deep(.el-dialog) {
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  background: #ffffff;
  overflow: hidden;
}

.modern-dialog :deep(.el-dialog__header) {
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-bottom: 1px solid #e9ecef;
  padding: 20px 24px 16px;
  margin: 0;
}

.modern-dialog :deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.modern-dialog :deep(.el-dialog__body) {
  padding: 24px;
  background: #ffffff;
}

.modern-dialog :deep(.el-dialog__footer) {
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
  padding: 16px 24px;
  margin: 0;
}

.dialog-content {
  padding: 0;
}

.dialog-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 12px;
  border: 1px solid #e9ecef;
}

.dialog-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

.dialog-title-text h3 {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.dialog-title-text p {
  margin: 0;
  font-size: 14px;
  color: #6c757d;
}

.modern-form {
  margin-top: 0;
}

.modern-form :deep(.el-form-item) {
  margin-bottom: 20px;
}

.modern-form :deep(.el-form-item__label) {
  font-weight: 500;
  color: #495057;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 8px;
}

.form-row {
  margin-bottom: 16px;
}

.form-row:last-child {
  margin-bottom: 0;
}

.modern-form :deep(.el-input__wrapper) {
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}

.modern-form :deep(.el-input__wrapper:hover) {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
}

.modern-form :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
}

.modern-form :deep(.el-select .el-input__wrapper) {
  border-radius: 8px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 0;
}

.cancel-btn {
  height: 40px;
  padding: 0 20px;
  border-radius: 8px;
  font-weight: 500;
  background: #ffffff;
  border: 1px solid #d9d9d9;
  color: #666666;
  transition: all 0.3s ease;
}

.cancel-btn:hover {
  background: #f5f5f5;
  border-color: #b3b3b3;
  color: #333333;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.confirm-btn {
  height: 40px;
  padding: 0 20px;
  border-radius: 8px;
  font-weight: 500;
  background: linear-gradient(135deg, #409eff 0%, #3a8ee6 100%);
  border: none;
  transition: all 0.3s ease;
}

.confirm-btn:hover {
  background: linear-gradient(135deg, #3a8ee6 0%, #337ecc 100%);
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(64, 158, 255, 0.3);
}

.room-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

@media (max-width: 768px) {
  .modern-dialog :deep(.el-dialog) {
    width: 95% !important;
    margin: 0 auto;
  }
  
  .dialog-header {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }
  
  .modern-form :deep(.el-row) {
    flex-direction: column;
  }
  
  .modern-form :deep(.el-col) {
    width: 100% !important;
    margin-bottom: 16px;
  }
}
</style>